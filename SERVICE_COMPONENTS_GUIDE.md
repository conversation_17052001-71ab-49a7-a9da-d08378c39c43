# 服务组件使用指南 / Service Components Guide

本文档介绍了基于Figma设计图实现的服务页面组件，包括核心业务展示和服务卡片两个主要组件。

## 组件概览 / Component Overview

### 1. ServiceCoreBusiness 组件
- **文件位置**: `src/components/ServiceCoreBusiness.vue`
- **功能**: 展示核心业务信息，包括标题、副标题和描述
- **设计来源**: Figma设计图第一部分 (node-id: 3083-3652)

### 2. ServiceCards 组件
- **文件位置**: `src/components/ServiceCards.vue`
- **功能**: 展示三个主要服务的详细信息卡片
- **设计来源**: Figma设计图第二部分 (node-id: 3083-3663)

### 3. ServiceCoreServices 组件
- **文件位置**: `src/components/ServiceCoreServices.vue`
- **功能**: 展示核心服务信息，包含复杂的背景装饰和品牌标识
- **设计来源**: Figma设计图第三部分 (node-id: 3083-3725)

## 国际化支持 / Internationalization Support

两个组件都完全支持国际化，包含以下语言：
- 🇨🇳 中文 (zh)
- 🇺🇸 英文 (en)
- 🇯🇵 日文 (ja)
- 🇰🇷 韩文 (ko)

### 国际化配置文件
所有翻译文本都存储在 `src/i18n/locales/` 目录下：
- `zh.js` - 中文翻译
- `en.js` - 英文翻译
- `ja.js` - 日文翻译
- `ko.js` - 韩文翻译

## 使用方法 / Usage

### 在页面中使用组件

```vue
<template>
  <div class="services-page">
    <!-- 核心业务组件 -->
    <ServiceCoreBusiness />

    <!-- 服务卡片组件 -->
    <ServiceCards />

    <!-- 核心服务组件 -->
    <ServiceCoreServices />
  </div>
</template>

<script setup>
import ServiceCoreBusiness from '@/components/ServiceCoreBusiness.vue'
import ServiceCards from '@/components/ServiceCards.vue'
import ServiceCoreServices from '@/components/ServiceCoreServices.vue'
</script>
```

### 演示页面
访问 `/service-demo` 路由可以查看完整的组件演示，包括：
- 实时语言切换功能
- 响应式设计展示
- 组件特性说明

## 设计规格 / Design Specifications

### ServiceCoreBusiness 组件
- **容器宽度**: 1320px (最大)
- **内容高度**: 600px (最小)
- **标题字体**: 32px / Bold
- **副标题字体**: 100px / Light (250)
- **描述字体**: 20px / Light (300)
- **分隔线**: 30px × 2px

### ServiceCards 组件
- **卡片背景**: rgba(24, 24, 24, 0.8) + 毛玻璃效果
- **卡片内边距**: 60px
- **卡片间距**: 60px
- **标题字体**: 48px / Bold
- **编号字体**: 240px / AvantGarde CE
- **特性字体**: 20px / Bold
- **描述字体**: 20px / Light

### ServiceCoreServices 组件
- **整体尺寸**: 1440px × 2045px
- **背景装饰**: 11个同心圆 + 5个装饰图标
- **主卡片**: 520px × 450px (深色背景)
- **次卡片**: 380px × 450px / 780px × 300px (浅色背景)
- **品牌标识**: 220px × 220px 圆形设计
- **装饰元素**: 4个几何形状装饰

## 响应式设计 / Responsive Design

### 断点设置
- **桌面**: ≥1280px
- **平板**: 768px - 1279px
- **手机**: ≤767px

### 适配特性
- ✅ 完全响应式布局
- ✅ 移动端优化的字体大小
- ✅ 灵活的网格系统
- ✅ 触摸友好的交互

## 字体优化 / Font Optimization

### 中文字体
- **主要字体**: Alibaba PuHuiTi 2.0
- **备用字体**: PingFang SC, Hiragino Sans GB, Microsoft YaHei

### 英文字体
- **主要字体**: Inter Variable Font
- **备用字体**: Helvetica Neue, Arial

### 多语言排版
- 中文: 标准行高和字间距
- 英文: 优化的行高 (1.4-1.6em)
- 日文: 增加字间距 (0.02-0.05em)
- 韩文: 适中的字间距 (0.01-0.03em)

## 技术特性 / Technical Features

- ✅ Vue 3 Composition API
- ✅ Vue I18n 国际化
- ✅ SCSS 模块化样式
- ✅ 响应式设计
- ✅ 毛玻璃效果
- ✅ 动态内容渲染
- ✅ 复杂背景装饰
- ✅ 品牌标识集成
- ✅ 多层次视觉设计
- ✅ 类型安全
- ✅ 可测试性

## 测试 / Testing

测试文件位置：
- `src/components/__tests__/ServiceCoreBusiness.test.js`
- `src/components/__tests__/ServiceCards.test.js`
- `src/components/__tests__/ServiceCoreServices.test.js`

运行测试（需要配置测试环境）：
```bash
npm run test
```

## 文件结构 / File Structure

```
src/
├── components/
│   ├── ServiceCoreBusiness.vue     # 核心业务组件
│   ├── ServiceCards.vue           # 服务卡片组件
│   ├── ServiceCoreServices.vue    # 核心服务组件
│   └── __tests__/
│       ├── ServiceCoreBusiness.test.js
│       ├── ServiceCards.test.js
│       └── ServiceCoreServices.test.js
├── i18n/
│   └── locales/
│       ├── zh.js                  # 中文翻译
│       ├── en.js                  # 英文翻译
│       ├── ja.js                  # 日文翻译
│       └── ko.js                  # 韩文翻译
├── views/
│   ├── Services.vue               # 服务页面
│   └── ServiceDemo.vue            # 演示页面
└── styles/
    ├── variables.scss             # 样式变量
    ├── mixins.scss               # 样式混合器
    └── fonts.scss                # 字体定义
```

## 浏览器兼容性 / Browser Compatibility

- ✅ Chrome 88+
- ✅ Firefox 85+
- ✅ Safari 14+
- ✅ Edge 88+

## 性能优化 / Performance Optimization

- 组件懒加载
- 图片优化
- CSS 压缩
- 字体预加载
- 响应式图片

## 维护说明 / Maintenance Notes

1. **添加新语言**: 在 `src/i18n/locales/` 目录下添加新的语言文件
2. **修改样式**: 使用 SCSS 变量和混合器保持一致性
3. **更新内容**: 修改国际化文件中的翻译文本
4. **测试**: 确保在所有支持的语言和设备上测试

## 联系信息 / Contact

如有问题或建议，请联系开发团队。
