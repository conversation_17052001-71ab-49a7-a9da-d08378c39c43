<template>
  <div
    class="fullscreen-menu"
    :class="{ 'is-open': isOpen }"
    @click="handleBackdropClick"
  >
    <div class="menu-content" @click.stop>
      <!-- Home Icon -->
      <div class="home-icon">
        <router-link to="/" @click="handleMenuClick">
          <img src="../assets/imgs/home.png" alt="Home" />
        </router-link>
      </div>

      <!-- Menu Items -->
      <div class="menu-items">
        <div v-for="item in menuItems" :key="item.id" class="menu-item-group">
          <!-- Main Menu Item -->
          <div class="main-menu-item">
            <router-link
              :to="item.path"
              class="menu-link main-link"
              @click="handleMenuClick"
            >
              {{ getMenuTitle(item) }}
            </router-link>
          </div>

          <!-- Sub Menu Items -->
          <div v-if="item.hasSubMenu && item.subMenu" class="sub-menu">
            <div class="sub-menu-items">
              <router-link
                v-for="subItem in item.subMenu"
                :key="subItem.id"
                :to="subItem.path"
                class="menu-link sub-link"
                @click="handleMenuClick"
              >
                {{ subItem.name }}
              </router-link>
            </div>
          </div>
        </div>
      </div>

      <!-- Language Switcher -->
      <div class="language-switcher">
        <!-- Mobile Language Options -->
        <div class="mobile-language-switcher mobile-only">
          <h3 class="language-title">Language / 语言</h3>
          <div class="mobile-language-options">
            <button
              v-for="lang in languages"
              :key="lang.code"
              class="mobile-language-option"
              :class="{ 'is-active': currentLocale === lang.code }"
              @click="changeLanguage(lang.code)"
            >
              {{ getLanguageName(lang) }}
            </button>
          </div>
        </div>
      </div>
      <div class="close-fullscreen mobile-only">
        <img
          src="../assets/imgs/close.png"
          alt="Close"
          @click="handleMenuClick"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import { useI18n } from "vue-i18n";
import { useLanguageStore } from "@/stores/language";
import { getMenuData } from "../api/menu.js";

// 定义 props
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
});

// 定义 emits
const emit = defineEmits(["close"]);

const { t } = useI18n();
const languageStore = useLanguageStore();
const menuItems = ref([]);
const loading = ref(false);

// 使用store中的语言数据
const languages = computed(() => languageStore.supportedLanguages);
const currentLocale = computed(() => languageStore.currentLanguage);
const currentLanguageCode = computed(() => languageStore.currentLanguageCode);

const getMenuTitle = (item) => {
  return item.title[currentLocale.value] || item.title.en;
};

const getLanguageName = (lang) => {
  return t(`language.${lang.name}`);
};

const changeLanguage = async (langCode) => {
  await languageStore.changeLanguage(langCode);
  emit("close");
};

const handleBackdropClick = () => {
  emit("close");
};

const handleMenuClick = () => {
  emit("close");
};

const loadMenuData = async () => {
  try {
    loading.value = true;
    const response = await getMenuData();
    if (response.success) {
      menuItems.value = response.data.mainMenu;
    }
  } catch (error) {
    console.error("Failed to load menu data:", error);
  } finally {
    loading.value = false;
  }
};

// 监听菜单打开状态，加载数据
watch(
  () => props.isOpen,
  (newValue) => {
    if (newValue && menuItems.value.length === 0) {
      loadMenuData();
    }
  }
);

onMounted(() => {
  if (props.isOpen) {
    loadMenuData();
  }
});
</script>

<style lang="scss" scoped>
.fullscreen-menu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100vw;
  background: rgba($primary-color, 0.9);
  backdrop-filter: blur(15px);
  z-index: $z-index-modal;
  opacity: 0;
  visibility: hidden;
  @include transition;
  cursor: pointer;

  &.is-open {
    height: 378px;
    opacity: 1;
    visibility: visible;

    @include medium-only {
      height: 100vh;
    }
    @include tablet-only {
      height: 100vh;
    }
  }
}

.menu-content {
  @include flex-between;
  align-items: flex-start;
  padding: 34px $container-padding-desktop $spacing-3xl;
  height: 100%;
  max-width: $container-wide-width;
  margin: 0 auto;
  cursor: default;

  @include medium-only {
    @include flex-column;
  }

  @include tablet-only {
    @include flex-column;
    padding: $spacing-lg;
    gap: $spacing-2xl;
  }
}

.home-icon {
  opacity: 0.7;

  img {
    width: 24px;
    height: 24px;
  }
}

.menu-items {
  display: flex;
  justify-content: center;
  gap: 68px;
  flex: 1;

  @include desktop-only {
    gap: $spacing-2xl;
  }
  @include medium-only {
    width: 100%;
    height: calc(100vh - 300px);
    overflow-y: scroll;
    @include flex-column;
    gap: $spacing-3xl;
    align-items: center;
    justify-content: flex-start;
    @include scrollbar-thin;
  }

  @include tablet-only {
    height: calc(100vh - 220px);
  }
}

.menu-item-group {
  @include flex-column;
  align-items: center;
  gap: $spacing-2xl;

  @include tablet-only {
    gap: $spacing-2xl;
  }
}

.main-menu-item {
  .main-link {
    @include font-primary($font-size-xl, $font-weight-bold);
    color: $text-white;
    text-decoration: none;
    opacity: 0.7;
    @include transition(opacity);

    &:hover,
    &.router-link-active {
      opacity: 1;
    }
  }
}

.sub-menu {
  .sub-menu-items {
    @include flex-column;
    align-items: center;
    gap: $spacing-lg;

    .sub-link {
      @include font-primary($font-size-base, $font-weight-light);
      color: rgba($text-white, 0.8);
      text-decoration: none;
      @include transition;

      &:hover,
      &.featured,
      &.router-link-active {
        font-weight: $font-weight-semibold;
        color: $text-white;
      }
    }
  }
}

.mobile-only {
  display: none;

  @include medium-only {
    display: block;
    margin: 0 auto;
  }
}

.close-fullscreen {
  margin-top: 10px;
  img {
    width: 30px;
    height: 30px;
  }
}

// 移动端语言切换器
.mobile-language-switcher {
  width: 648px;
  margin: 0 auto;
  padding-top: $spacing-2xl;
  border-top: 1px solid rgba($text-white, 0.2);

  .language-title {
    @include font-primary($font-size-base, $font-weight-semibold);
    color: rgba($text-white, 0.6);
    margin: 0 0 $spacing-md 0;
    @include text-center;
  }

  .mobile-language-options {
    @include grid-responsive(1fr, $spacing-sm);
    grid-template-columns: 1fr 1fr;
  }

  .mobile-language-option {
    @include button-reset;
    padding: $spacing-sm $spacing-md;
    border: 2px solid rgba($text-white, 0.2);
    border-radius: $border-radius-md;
    color: rgba($text-white, 0.8);
    @include font-primary($font-size-sm, $font-weight-medium);
    @include transition(all, $transition-fast);
    @include text-center;

    &:hover {
      border-color: rgba($text-white, 0.5);
      background-color: rgba($text-white, 0.1);
      color: $text-white;
    }

    &.is-active {
      border-color: $text-white;
      background-color: $text-white;
      color: $primary-color;
    }
  }
  @include mobile-only {
    width: clamp(340px, 390px, 680px);
  }
}
</style>
