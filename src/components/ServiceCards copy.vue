<template>
  <section class="service-cards">
    <div class="container">
      <div class="cards-wrapper">
        <!-- 全球发行与运营卡片 -->
        <div class="service-card">
          <div class="card-header">
            <h3 class="card-title">
              {{ $t("services.serviceCards.globalDistribution.title") }}
            </h3>
            <span class="card-number">{{
              $t("services.serviceCards.globalDistribution.number")
            }}</span>
          </div>
          <div class="card-content">
            <div class="features-section">
              <div
                v-for="(feature, index) in $tm(
                  'services.serviceCards.globalDistribution.features'
                )"
                :key="index"
              >
                <div class="bullet-points">
                  <span class="bullet">·</span>
                </div>
                <div class="features-list">
                  <div class="feature-item">
                    {{ feature }}
                  </div>
                </div>
              </div>
              <div class="descriptions-list">
                <div
                  v-for="(description, index) in $tm(
                    'services.serviceCards.globalDistribution.descriptions'
                  )"
                  :key="index"
                  class="description-item"
                >
                  {{ description }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 词曲作者全链服务卡片 -->
        <div class="service-card">
          <div class="card-header">
            <h3 class="card-title">
              {{ $t("services.serviceCards.songwriterServices.title") }}
            </h3>
            <span class="card-number secondary">{{
              $t("services.serviceCards.songwriterServices.number")
            }}</span>
          </div>
          <div class="card-content">
            <div class="features-section">
              <div class="bullet-points">
                <span v-for="n in 4" :key="n" class="bullet">·</span>
              </div>
              <div class="features-list">
                <div
                  v-for="(feature, index) in $tm(
                    'services.serviceCards.songwriterServices.features'
                  )"
                  :key="index"
                  class="feature-item"
                >
                  {{ feature }}
                </div>
              </div>
              <div class="descriptions-list">
                <div
                  v-for="(description, index) in $tm(
                    'services.serviceCards.songwriterServices.descriptions'
                  )"
                  :key="index"
                  class="description-item"
                >
                  {{ description }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 艺人音乐全案服务卡片 -->
        <div class="service-card">
          <div class="card-header">
            <h3 class="card-title">
              {{ $t("services.serviceCards.artistServices.title") }}
            </h3>
            <span class="card-number secondary">{{
              $t("services.serviceCards.artistServices.number")
            }}</span>
          </div>
          <div class="card-content">
            <div class="features-section">
              <div class="bullet-points">
                <span v-for="n in 4" :key="n" class="bullet">·</span>
              </div>
              <div class="features-list">
                <div
                  v-for="(feature, index) in $tm(
                    'services.serviceCards.artistServices.features'
                  )"
                  :key="index"
                  class="feature-item"
                >
                  {{ feature }}
                </div>
              </div>
              <div class="descriptions-list">
                <div
                  v-for="(description, index) in $tm(
                    'services.serviceCards.artistServices.descriptions'
                  )"
                  :key="index"
                  class="description-item"
                >
                  {{ description }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
</script>

<style lang="scss" scoped>
.service-cards {
  background-color: #181818;
  padding: 120px 60px;

  @include tablet-only {
    padding: 80px 40px;
  }

  @include mobile-only {
    padding: 60px 20px;
  }

  .container {
    max-width: $container-max-width;
    margin: 0 auto;
    border: 1px solid #fff;
  }

  .cards-wrapper {
    max-width: 1134px;
    background: url("../assets/imgs/services-bg-line.png") no-repeat
      center/cover;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 60px;

    @include tablet-only {
      gap: 40px;
      align-items: stretch;
    }

    @include mobile-only {
      gap: 30px;
    }
  }
}

.service-card {
  display: flex;
  flex-direction: column;
  gap: 80px;
  padding: 60px;
  background-color: rgba(24, 24, 24, 0.8);
  backdrop-filter: blur(4px);
  border-radius: $border-radius-lg;
  width: fit-content;
  max-width: 100%;

  @include tablet-only {
    gap: 60px;
    padding: 40px;
    width: 100%;
  }

  @include mobile-only {
    gap: 40px;
    padding: 30px;
  }

  .card-header {
    display: flex;
    justify-content: flex-end;
    align-items: flex-end;
    gap: -140px;
    padding-left: 40px;
    width: 720px;
    position: relative;

    @include tablet-only {
      width: 100%;
      gap: -100px;
      padding-left: 20px;
    }

    @include mobile-only {
      flex-direction: column;
      align-items: flex-start;
      gap: 20px;
      padding-left: 0;
    }
  }

  .card-title {
    @include font-chinese(48px, $font-weight-bold);
    color: $text-white;
    line-height: 1em;
    margin: 0;
    width: 331px;
    height: 70px;
    flex-shrink: 0;

    @include tablet-only {
      font-size: 36px;
      width: auto;
      height: auto;
    }

    @include mobile-only {
      font-size: 28px;
    }
  }

  .card-number {
    font-family: "AvantGarde CE", sans-serif;
    font-weight: 400;
    font-size: 240px;
    line-height: 1em;
    color: $text-white;
    opacity: 0.15;
    flex-shrink: 0;

    &.secondary {
      color: rgba(255, 255, 255, 0.2);
    }

    @include tablet-only {
      font-size: 180px;
      width: 200px;
    }

    @include mobile-only {
      font-size: 120px;
      width: 150px;
      opacity: 0.3;
    }
  }

  .card-content {
    width: 100%;
  }

  .features-section {
    display: flex;
    gap: 0;
    width: 100%;

    @include mobile-only {
      flex-direction: column;
      gap: 20px;
    }
  }

  .bullet-points {
    display: flex;
    flex-direction: column;
    gap: 10px;
    width: 40px;
    flex-shrink: 0;

    @include mobile-only {
      flex-direction: row;
      width: auto;
      gap: 20px;
    }

    .bullet {
      @include font-chinese(20px, $font-weight-bold);
      color: $text-white;
      line-height: 2em;
      width: 40px;
      text-align: center;

      @include mobile-only {
        width: auto;
      }
    }
  }

  .features-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding-right: 30px;
    flex-shrink: 0;

    @include mobile-only {
      padding-right: 0;
    }

    .feature-item {
      @include font-chinese(20px, $font-weight-bold);
      color: $text-white;
      line-height: 2em;
      width: 178px;

      @include tablet-only {
        width: auto;
        min-width: 150px;
      }

      @include mobile-only {
        width: auto;
        font-size: 18px;
      }
    }
  }

  .descriptions-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding-left: 30px;
    border-left: 1px solid $text-light;
    flex: 1;

    @include mobile-only {
      padding-left: 0;
      border-left: none;
      border-top: 1px solid $text-light;
      padding-top: 20px;
    }

    .description-item {
      @include font-chinese(20px, $font-weight-light);
      color: $text-white;
      line-height: 2em;

      @include mobile-only {
        font-size: 16px;
        line-height: 1.6em;
      }
    }
  }
}

// 多语言字体优化
:deep(.service-cards) {
  // 英文环境下的字体优化
  &[lang="en"],
  &[data-lang="en"] {
    .card-title {
      @include font-english(48px, $font-weight-bold);

      @include tablet-only {
        font-size: 36px;
      }

      @include mobile-only {
        font-size: 28px;
      }
    }

    .feature-item {
      @include font-english(20px, $font-weight-bold);
      line-height: 1.4em;

      @include mobile-only {
        font-size: 18px;
      }
    }

    .description-item {
      @include font-english(20px, $font-weight-light);
      line-height: 1.6em;

      @include mobile-only {
        font-size: 16px;
        line-height: 1.4em;
      }
    }
  }

  // 日文环境下的字体优化
  &[lang="ja"],
  &[data-lang="ja"] {
    .card-title {
      @include font-chinese(48px, $font-weight-bold);
      letter-spacing: 0.05em;

      @include tablet-only {
        font-size: 36px;
      }

      @include mobile-only {
        font-size: 28px;
      }
    }

    .feature-item {
      @include font-chinese(20px, $font-weight-bold);
      letter-spacing: 0.02em;

      @include mobile-only {
        font-size: 18px;
      }
    }

    .description-item {
      @include font-chinese(20px, $font-weight-light);
      line-height: 1.8em;
      letter-spacing: 0.02em;

      @include mobile-only {
        font-size: 16px;
        line-height: 1.6em;
      }
    }
  }

  // 韩文环境下的字体优化
  &[lang="ko"],
  &[data-lang="ko"] {
    .card-title {
      @include font-chinese(48px, $font-weight-bold);
      letter-spacing: 0.03em;

      @include tablet-only {
        font-size: 36px;
      }

      @include mobile-only {
        font-size: 28px;
      }
    }

    .feature-item {
      @include font-chinese(20px, $font-weight-bold);
      letter-spacing: 0.01em;

      @include mobile-only {
        font-size: 18px;
      }
    }

    .description-item {
      @include font-chinese(20px, $font-weight-light);
      line-height: 1.7em;
      letter-spacing: 0.01em;

      @include mobile-only {
        font-size: 16px;
        line-height: 1.5em;
      }
    }
  }
}
</style>
