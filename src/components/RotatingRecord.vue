<template>
  <div class="rotating-record">
    <!-- 旋转的唱片SVG -->
    <svg
      class="record-svg"
      :class="{ 'is-rotating': isRotating }"
      width="120"
      height="120"
      viewBox="0 0 120 120"
      xmlns="http://www.w3.org/2000/svg"
    >
      <!-- 外圈 - 唱片边缘 -->
      <circle
        cx="60"
        cy="60"
        r="58"
        fill="#1a1a1a"
        stroke="#333"
        stroke-width="2"
      />

      <!-- 唱片表面纹理 - 多个同心圆 -->
      <circle
        cx="60"
        cy="60"
        r="50"
        fill="none"
        stroke="#2a2a2a"
        stroke-width="0.5"
        opacity="0.6"
      />
      <circle
        cx="60"
        cy="60"
        r="42"
        fill="none"
        stroke="#2a2a2a"
        stroke-width="0.5"
        opacity="0.6"
      />
      <circle
        cx="60"
        cy="60"
        r="34"
        fill="none"
        stroke="#2a2a2a"
        stroke-width="0.5"
        opacity="0.6"
      />
      <circle
        cx="60"
        cy="60"
        r="26"
        fill="none"
        stroke="#2a2a2a"
        stroke-width="0.5"
        opacity="0.6"
      />

      <!-- 中心标签区域 -->
      <circle
        cx="60"
        cy="60"
        r="18"
        fill="#313131"
        stroke="#555"
        stroke-width="1"
      />

      <!-- 中心孔 -->
      <circle cx="60" cy="60" r="3" fill="#000" />

      <!-- 标签文字 -->
      <text
        x="60"
        y="55"
        text-anchor="middle"
        fill="#868686"
        font-family="Alibaba PuHuiTi 2.0, sans-serif"
        font-size="8"
        font-weight="300"
      >
        轨迹
      </text>
      <text
        x="60"
        y="67"
        text-anchor="middle"
        fill="#868686"
        font-family="Alibaba PuHuiTi 2.0, sans-serif"
        font-size="6"
        font-weight="300"
      >
        RECORDS
      </text>
    </svg>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";

// 定义props
const props = defineProps({
  // 是否自动开始旋转
  autoStart: {
    type: Boolean,
    default: true,
  },
  // 旋转速度（秒）
  duration: {
    type: Number,
    default: 3,
  },
});

// 响应式数据
const isRotating = ref(false);

// 开始旋转
const startRotation = () => {
  isRotating.value = true;
};

// 停止旋转
const stopRotation = () => {
  isRotating.value = false;
};

// 生命周期
onMounted(() => {
  if (props.autoStart) {
    startRotation();
  }
});

// 暴露方法给父组件
defineExpose({
  startRotation,
  stopRotation,
});
</script>

<style lang="scss" scoped>
.rotating-record {
  @include flex-center;
  width: 120px;
  height: 120px;
  position: relative;

  // 添加一个微妙的光晕效果
  &::before {
    content: "";
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: radial-gradient(
      circle,
      rgba(49, 49, 49, 0.1) 0%,
      transparent 70%
    );
    border-radius: 50%;
    z-index: -1;
    opacity: 0.6;
  }
}

.record-svg {
  width: 100%;
  height: 100%;
  filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.15));
  @include transition(transform, 0.3s, ease);

  &.is-rotating {
    animation: rotate v-bind(duration + "s") linear infinite;
  }

  // 悬停效果
  &:hover {
    transform: scale(1.05);
  }
}

// 旋转动画
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 响应式调整
@include tablet-only {
  .rotating-record {
    width: 100px;
    height: 100px;
  }
}

@include mobile-only {
  .rotating-record {
    width: 80px;
    height: 80px;
  }
}
</style>
