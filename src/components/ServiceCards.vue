<template>
  <section class="service-cards">
    <div class="container">
      <div class="cards-wrapper">
        <!-- 全球发行与运营卡片 -->
        <div
          class="service-card card-float"
          data-aos="fade-up"
          data-aos-duration="1000"
          data-aos-delay="800"
        >
          <div class="card-header mt50">
            <h3 class="card-title">
              {{ $t("services.serviceCards.globalDistribution.title") }}
            </h3>
            <span class="card-number">01</span>
          </div>
          <div class="card-content">
            <div class="features-section">
              <ul>
                <li>
                  <div class="features-list">
                    <div class="feature-item">
                      {{
                        $t("services.serviceCards.globalDistribution.feature1")
                      }}
                    </div>
                    <div class="descriptions-item pb10">
                      {{
                        $t(
                          "services.serviceCards.globalDistribution.description1"
                        )
                      }}
                    </div>
                  </div>
                </li>
                <li>
                  <div class="features-list">
                    <div class="feature-item">
                      {{
                        $t("services.serviceCards.globalDistribution.feature2")
                      }}
                    </div>
                    <div class="descriptions-item pb10">
                      {{
                        $t(
                          "services.serviceCards.globalDistribution.description2"
                        )
                      }}
                    </div>
                  </div>
                </li>
                <li>
                  <div class="features-list">
                    <div class="feature-item">
                      {{
                        $t("services.serviceCards.globalDistribution.feature3")
                      }}
                    </div>
                    <div class="descriptions-item">
                      {{
                        $t(
                          "services.serviceCards.globalDistribution.description3"
                        )
                      }}
                    </div>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- 词曲作者全链服务卡片 -->
        <div class="service-card" data-aos="fade-up" data-aos-duration="1000">
          <div class="card-header">
            <h3 class="card-title right-88">
              {{ $t("services.serviceCards.songwriterServices.title") }}
            </h3>
            <span class="card-number secondary">02</span>
          </div>
          <div class="card-content">
            <div class="features-section">
              <ul>
                <li>
                  <div class="features-list">
                    <div class="feature-item item-w200">
                      {{
                        $t("services.serviceCards.songwriterServices.feature1")
                      }}
                    </div>
                    <div class="descriptions-item pb10">
                      {{
                        $t(
                          "services.serviceCards.songwriterServices.description1"
                        )
                      }}
                    </div>
                  </div>
                </li>
                <li>
                  <div class="features-list">
                    <div class="feature-item item-w200">
                      {{
                        $t("services.serviceCards.songwriterServices.feature2")
                      }}
                    </div>
                    <div class="descriptions-item pb10">
                      {{
                        $t(
                          "services.serviceCards.songwriterServices.description2"
                        )
                      }}
                    </div>
                  </div>
                </li>
                <li>
                  <div class="features-list">
                    <div class="feature-item item-w200">
                      {{
                        $t("services.serviceCards.songwriterServices.feature3")
                      }}
                    </div>
                    <div class="descriptions-item pb10">
                      {{
                        $t(
                          "services.serviceCards.songwriterServices.description3"
                        )
                      }}
                    </div>
                  </div>
                </li>
                <li>
                  <div class="features-list">
                    <div class="feature-item item-w200">
                      {{
                        $t("services.serviceCards.songwriterServices.feature4")
                      }}
                    </div>
                    <div class="descriptions-item">
                      {{
                        $t(
                          "services.serviceCards.songwriterServices.description4"
                        )
                      }}
                    </div>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- 艺人音乐全案服务卡片 -->
        <div
          class="service-card card-float"
          data-aos="fade-up"
          data-aos-duration="1000"
        >
          <div class="card-header">
            <h3 class="card-title right-88">
              {{ $t("services.serviceCards.artistServices.title") }}
            </h3>
            <span class="card-number secondary">03</span>
          </div>
          <div class="card-content">
            <div class="features-section">
              <ul>
                <li>
                  <div class="features-list">
                    <div class="feature-item item-w200">
                      {{ $t("services.serviceCards.artistServices.feature1") }}
                    </div>
                    <div class="descriptions-item pb10">
                      {{
                        $t("services.serviceCards.artistServices.description1")
                      }}
                    </div>
                  </div>
                </li>
                <li>
                  <div class="features-list">
                    <div class="feature-item item-w200">
                      {{ $t("services.serviceCards.artistServices.feature2") }}
                    </div>
                    <div class="descriptions-item pb10">
                      {{
                        $t("services.serviceCards.artistServices.description2")
                      }}
                    </div>
                  </div>
                </li>
                <li>
                  <div class="features-list">
                    <div class="feature-item item-w200">
                      {{ $t("services.serviceCards.artistServices.feature3") }}
                    </div>
                    <div class="descriptions-item pb10">
                      {{
                        $t("services.serviceCards.artistServices.description3")
                      }}
                    </div>
                  </div>
                </li>
                <li>
                  <div class="features-list">
                    <div class="feature-item item-w200">
                      {{ $t("services.serviceCards.artistServices.feature4") }}
                    </div>
                    <div class="descriptions-item">
                      {{
                        $t("services.serviceCards.artistServices.description4")
                      }}
                    </div>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
</script>

<style lang="scss" scoped>
.service-cards {
  background-color: #181818;
  padding: 0px 60px;

  @include tablet-only {
    padding: 0px 20px;
  }

  @include mobile-only {
    padding: 0px 20px;
  }

  .container {
    max-width: 1134px;
    height: 2408px;
    background: #181818 url("../assets/imgs/services-bg-line.png") no-repeat;
    background-size: 100%;
    margin: 0 auto;

    @include mobile-only {
      height: auto !important;
    }
  }

  .cards-wrapper {
    content: "";
    display: table;
    clear: both;

    @media screen and (max-width: 1024px) {
      display: block;
      clear: both;
    }
  }
}

.card-float {
  float: right;

  &::after {
    clear: both;
  }

  @media screen and (max-width: 1024px) {
    float: none;
  }
}

.service-card {
  // border: 1px solid #fff;
  display: flex;
  flex-direction: column;
  gap: 60px;
  padding: 120px 0 60px;
  margin-bottom: 40px;
  width: fit-content;
  max-width: 100%;

  @media screen and (max-width: 1024px) {
    margin-bottom: 0px;
  }

  @include mobile-only {
    gap: 40px;
    padding: 30px 0;
  }

  .card-header {
    display: flex;
    justify-content: flex-end;
    position: relative;

    @include mobile-only {
      flex-direction: column;
      align-items: flex-start;
      gap: 20px;
      padding-left: 0;
    }
  }

  .card-title {
    @include font-chinese(48px, $font-weight-bold);
    color: $text-white;
    line-height: 1em;
    position: absolute;
    top: 160px;
    right: 135px;
    flex-shrink: 0;

    @include mobile-only {
      font-size: 24px;
      top: 75px;
      right: 0px;
      left: 0;
    }
  }

  .card-number {
    font-family: "AvantGarde CE", "Inter", sans-serif;
    font-weight: 400;
    font-size: 240px;
    line-height: 1em;
    color: $text-white;
    opacity: 0.15;
    flex-shrink: 0;

    @include mobile-only {
      font-size: 120px;
      width: 150px;
      opacity: 0.3;
    }
  }

  .card-content {
    width: 100%;
  }

  ul {
    margin-top: 0px;
    li {
      color: $text-white;
      list-style-type: disc;

      .features-list {
        display: flex;
        gap: 20px;
        padding-right: 30px;
        flex-shrink: 0;

        @include mobile-only {
          flex-direction: column;
          padding-right: 0;
          padding-bottom: 15px;
          gap: 5px;
        }
      }

      .feature-item {
        width: 188px;
        padding-left: 20px;
        @include font-chinese(20px, $font-weight-bold);
        color: $text-white;
        line-height: 2em;

        @include mobile-only {
          padding-left: 2px;
          width: auto;
          font-size: 18px;
        }
      }

      .item-w200 {
        width: 200px;
      }

      .descriptions-item {
        width: 472px;
        padding-left: 30px;
        border-left: 1px solid $text-light;
        @include font-chinese(20px, $font-weight-light);
        color: $text-white;
        line-height: 2em;

        @include mobile-only {
          width: 100%;
          padding-left: 0;
          border-left: none;
          border-top: 0;
          font-size: 18px;
        }
      }
    }
  }
  .pb10 {
    padding-bottom: 10px;
  }

  .mt50 {
    margin-top: 50px;
  }

  .right-88 {
    right: 88px;
  }
}
</style>
