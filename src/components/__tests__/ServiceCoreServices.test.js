import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import { createI18n } from 'vue-i18n'
import ServiceCoreServices from '../ServiceCoreServices.vue'

// 创建测试用的i18n实例
const i18n = createI18n({
  legacy: false,
  locale: 'zh',
  globalInjection: true,
  messages: {
    zh: {
      services: {
        coreServices: {
          title: '核心服务',
          subtitle: '让内容真正变成资产',
          services: [
            {
              title: '一体化版权管理系统（合作即享）',
              description: '从作品清单梳理、合同管理到上线授权与收益分账，实现版权资产化、数据化、可视化。',
              type: 'primary'
            },
            {
              title: '侵权维权全流程代理',
              description: '涵盖音视频、电商、出版、直播等场景，提供高效、专业的法律代理服务。',
              type: 'secondary'
            },
            {
              title: '全球变现通路',
              description: '与全球主流音乐平台、电信运营商、电商平台等构建多元授权合作，帮助创作者拓展传统之外的持续收益空间。',
              type: 'secondary'
            },
            {
              title: '平台直连机制',
              description: '与主流平台建立长期合作关系，确保版权问题直通响应、快速处理。',
              type: 'secondary'
            },
            {
              title: '内容+商业双轮驱动',
              description: '从艺术表达到商业转化，助力音乐人作品在市场中实现可持续价值增长。',
              type: 'secondary'
            }
          ]
        }
      }
    },
    en: {
      services: {
        coreServices: {
          title: 'Core Services',
          subtitle: 'Making Content Truly Become Assets',
          services: [
            {
              title: 'Integrated Copyright Management System (Enjoy Upon Cooperation)',
              description: 'From work inventory sorting, contract management to online authorization and revenue sharing, achieving copyright assetization, digitization, and visualization.',
              type: 'primary'
            },
            {
              title: 'Full-Process Infringement Rights Protection Agency',
              description: 'Covering audio-visual, e-commerce, publishing, live streaming and other scenarios, providing efficient and professional legal agency services.',
              type: 'secondary'
            },
            {
              title: 'Global Monetization Channels',
              description: 'Building diversified licensing cooperation with global mainstream music platforms, telecom operators, e-commerce platforms, etc., helping creators expand sustainable revenue space beyond traditional channels.',
              type: 'secondary'
            },
            {
              title: 'Platform Direct Connection Mechanism',
              description: 'Establishing long-term cooperative relationships with mainstream platforms to ensure direct response and rapid processing of copyright issues.',
              type: 'secondary'
            },
            {
              title: 'Content + Business Dual-Wheel Drive',
              description: 'From artistic expression to commercial transformation, helping musicians achieve sustainable value growth of their works in the market.',
              type: 'secondary'
            }
          ]
        }
      }
    }
  }
})

describe('ServiceCoreServices', () => {
  it('renders correctly with Chinese content', () => {
    const wrapper = mount(ServiceCoreServices, {
      global: {
        plugins: [i18n]
      }
    })

    expect(wrapper.find('.section-title').text()).toBe('核心服务')
    expect(wrapper.find('.section-subtitle').text()).toBe('让内容真正变成资产')
    expect(wrapper.text()).toContain('一体化版权管理系统（合作即享）')
    expect(wrapper.text()).toContain('侵权维权全流程代理')
  })

  it('renders correctly with English content', async () => {
    i18n.global.locale.value = 'en'
    
    const wrapper = mount(ServiceCoreServices, {
      global: {
        plugins: [i18n]
      }
    })

    await wrapper.vm.$nextTick()

    expect(wrapper.find('.section-title').text()).toBe('Core Services')
    expect(wrapper.find('.section-subtitle').text()).toBe('Making Content Truly Become Assets')
    expect(wrapper.text()).toContain('Integrated Copyright Management System')
    expect(wrapper.text()).toContain('Full-Process Infringement Rights Protection Agency')
  })

  it('has correct structure and classes', () => {
    const wrapper = mount(ServiceCoreServices, {
      global: {
        plugins: [i18n]
      }
    })

    expect(wrapper.find('.service-core-services').exists()).toBe(true)
    expect(wrapper.find('.background-decoration').exists()).toBe(true)
    expect(wrapper.find('.title-section').exists()).toBe(true)
    expect(wrapper.find('.services-grid').exists()).toBe(true)
    expect(wrapper.find('.main-service-card').exists()).toBe(true)
    expect(wrapper.find('.secondary-services').exists()).toBe(true)
    expect(wrapper.find('.brand-section').exists()).toBe(true)
  })

  it('renders background decoration circles', () => {
    const wrapper = mount(ServiceCoreServices, {
      global: {
        plugins: [i18n]
      }
    })

    const circles = wrapper.findAll('.circle')
    expect(circles.length).toBeGreaterThan(0)
    
    const decorationIcons = wrapper.findAll('.decoration-icons .icon')
    expect(decorationIcons.length).toBe(5)
  })

  it('renders service cards correctly', () => {
    const wrapper = mount(ServiceCoreServices, {
      global: {
        plugins: [i18n]
      }
    })

    // 检查主要服务卡片
    expect(wrapper.find('.main-service-card').exists()).toBe(true)
    expect(wrapper.find('.primary-icon').exists()).toBe(true)

    // 检查次要服务卡片
    const secondaryCards = wrapper.findAll('.secondary-card')
    expect(secondaryCards.length).toBe(4) // 4个次要服务卡片

    // 检查卡片图标
    const secondaryIcons = wrapper.findAll('.secondary-icon')
    expect(secondaryIcons.length).toBe(4)
  })

  it('renders brand section correctly', () => {
    const wrapper = mount(ServiceCoreServices, {
      global: {
        plugins: [i18n]
      }
    })

    expect(wrapper.find('.brand-section').exists()).toBe(true)
    expect(wrapper.find('.brand-logo').exists()).toBe(true)
    expect(wrapper.find('.logo-text').text()).toBe('Sea Track')
    
    const logoCircles = wrapper.findAll('.logo-circle')
    expect(logoCircles.length).toBe(7)
    
    const decorations = wrapper.findAll('.decoration')
    expect(decorations.length).toBe(4)
  })

  it('has responsive design classes', () => {
    const wrapper = mount(ServiceCoreServices, {
      global: {
        plugins: [i18n]
      }
    })

    const component = wrapper.find('.service-core-services')
    expect(component.exists()).toBe(true)
    
    // 检查是否有响应式相关的样式类
    expect(wrapper.find('.services-grid').exists()).toBe(true)
    expect(wrapper.find('.secondary-cards-row').exists()).toBe(true)
  })

  it('renders all service content correctly', () => {
    const wrapper = mount(ServiceCoreServices, {
      global: {
        plugins: [i18n]
      }
    })

    // 检查所有5个服务是否都被渲染
    const cardTitles = wrapper.findAll('.card-title')
    expect(cardTitles.length).toBe(5)
    
    const cardDescriptions = wrapper.findAll('.card-description')
    expect(cardDescriptions.length).toBe(5)
  })
})
