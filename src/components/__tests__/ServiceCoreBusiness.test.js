import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import { createI18n } from 'vue-i18n'
import ServiceCoreBusiness from '../ServiceCoreBusiness.vue'

// 创建测试用的i18n实例
const i18n = createI18n({
  legacy: false,
  locale: 'zh',
  messages: {
    zh: {
      services: {
        coreBusiness: {
          title: '核心业务',
          subtitle: '赋能音乐全生态',
          description: '海之轨迹围绕"音乐内容即资产"的理念，海之轨迹构建系统化、全球化、多场景的音乐商业服务体系'
        }
      }
    },
    en: {
      services: {
        coreBusiness: {
          title: 'Core Business',
          subtitle: 'Empowering the Music Ecosystem',
          description: 'Centered around the concept of "music content as assets", Sea Tracks builds a systematic, global, multi-scenario music business service system'
        }
      }
    }
  }
})

describe('ServiceCoreBusiness', () => {
  it('renders correctly with Chinese content', () => {
    const wrapper = mount(ServiceCoreBusiness, {
      global: {
        plugins: [i18n]
      }
    })

    expect(wrapper.find('.section-title').text()).toBe('核心业务')
    expect(wrapper.find('.section-subtitle').text()).toBe('赋能音乐全生态')
    expect(wrapper.find('.description-text').text()).toContain('海之轨迹围绕')
  })

  it('renders correctly with English content', async () => {
    i18n.global.locale.value = 'en'
    
    const wrapper = mount(ServiceCoreBusiness, {
      global: {
        plugins: [i18n]
      }
    })

    await wrapper.vm.$nextTick()

    expect(wrapper.find('.section-title').text()).toBe('Core Business')
    expect(wrapper.find('.section-subtitle').text()).toBe('Empowering the Music Ecosystem')
    expect(wrapper.find('.description-text').text()).toContain('Sea Tracks builds')
  })

  it('has correct structure and classes', () => {
    const wrapper = mount(ServiceCoreBusiness, {
      global: {
        plugins: [i18n]
      }
    })

    expect(wrapper.find('.service-core-business').exists()).toBe(true)
    expect(wrapper.find('.content-wrapper').exists()).toBe(true)
    expect(wrapper.find('.title-section').exists()).toBe(true)
    expect(wrapper.find('.description-section').exists()).toBe(true)
    expect(wrapper.find('.title-divider').exists()).toBe(true)
  })

  it('has responsive layout structure', () => {
    const wrapper = mount(ServiceCoreBusiness, {
      global: {
        plugins: [i18n]
      }
    })

    const contentWrapper = wrapper.find('.content-wrapper')
    expect(contentWrapper.classes()).toContain('content-wrapper')
    
    const titleSection = wrapper.find('.title-section')
    expect(titleSection.exists()).toBe(true)
    
    const descriptionSection = wrapper.find('.description-section')
    expect(descriptionSection.exists()).toBe(true)
  })
})
