import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import { createI18n } from 'vue-i18n'
import ServiceCards from '../ServiceCards.vue'

// 创建测试用的i18n实例
const i18n = createI18n({
  legacy: false,
  locale: 'zh',
  messages: {
    zh: {
      services: {
        serviceCards: {
          globalDistribution: {
            title: '全球发行与运营',
            number: '01',
            features: [
              '一键触达全球',
              '全流程专业护航',
              '最大化作品可见度'
            ],
            descriptions: [
              '覆盖30+国内外主流音乐及社交平台',
              '提供从版权授权、物料制作、平台上线到内容维护的全流程发行支持',
              '确保您的音乐作品快速上线、全球可见'
            ]
          },
          songwriterServices: {
            title: '词曲作者全链服务',
            number: '02',
            features: [
              '构建个人作品版权库',
              '收益透明可视',
              '多元化变现路径',
              '权益守护者'
            ],
            descriptions: [
              '系统化管理作品，清晰掌握版权归属',
              '实时追踪全球播放、授权与收益数据',
              '深度运营影视授权、翻唱授权、商业授权等10+丰富场景',
              '提供全网数据监测与法律维权代理，为您的创作保驾护航'
            ]
          },
          artistServices: {
            title: '艺人音乐全案服务',
            number: '03',
            features: [
              '定制化音乐企划',
              '专业制作支持',
              '热歌与影视OST定制',
              '一体化宣发方案'
            ],
            descriptions: [
              '从概念到执行，深度挖掘艺人特质与市场潜力',
              '涵盖EP/专辑制作、MV创意拍摄，打造高品质视听体验',
              '精准把握市场脉搏，创作契合短视频传播与影视剧情的爆款作品',
              '整合资源，实现从内容创作到市场推广的无缝衔接'
            ]
          }
        }
      }
    },
    en: {
      services: {
        serviceCards: {
          globalDistribution: {
            title: 'Global Distribution & Operations',
            number: '01',
            features: [
              'One-click global reach',
              'Full-process professional support',
              'Maximize work visibility'
            ],
            descriptions: [
              'Covering 30+ mainstream music and social platforms worldwide',
              'Providing full-process distribution support from copyright licensing, material production, platform launch to content maintenance',
              'Ensuring your music works go online quickly and are globally visible'
            ]
          },
          songwriterServices: {
            title: 'Songwriter Full-Chain Services',
            number: '02',
            features: [
              'Build personal copyright library',
              'Transparent revenue visibility',
              'Diversified monetization paths',
              'Rights guardian'
            ],
            descriptions: [
              'Systematically manage works and clearly grasp copyright ownership',
              'Real-time tracking of global playback, licensing and revenue data',
              'Deep operation of 10+ rich scenarios including film and TV licensing, cover licensing, commercial licensing',
              'Providing network-wide data monitoring and legal rights protection agency to escort your creation'
            ]
          },
          artistServices: {
            title: 'Artist Music Full-Service Solutions',
            number: '03',
            features: [
              'Customized music planning',
              'Professional production support',
              'Hit songs & film OST customization',
              'Integrated promotion solutions'
            ],
            descriptions: [
              'From concept to execution, deeply exploring artist characteristics and market potential',
              'Covering EP/album production, MV creative shooting, creating high-quality audiovisual experiences',
              'Accurately grasping market pulse, creating hit works that fit short video communication and film plot',
              'Integrating resources to achieve seamless connection from content creation to market promotion'
            ]
          }
        }
      }
    }
  }
})

describe('ServiceCards', () => {
  it('renders correctly with Chinese content', () => {
    const wrapper = mount(ServiceCards, {
      global: {
        plugins: [i18n]
      }
    })

    // 检查是否渲染了三个服务卡片
    const serviceCards = wrapper.findAll('.service-card')
    expect(serviceCards).toHaveLength(3)

    // 检查第一个卡片的内容
    expect(wrapper.text()).toContain('全球发行与运营')
    expect(wrapper.text()).toContain('01')
    expect(wrapper.text()).toContain('一键触达全球')

    // 检查第二个卡片的内容
    expect(wrapper.text()).toContain('词曲作者全链服务')
    expect(wrapper.text()).toContain('02')
    expect(wrapper.text()).toContain('构建个人作品版权库')

    // 检查第三个卡片的内容
    expect(wrapper.text()).toContain('艺人音乐全案服务')
    expect(wrapper.text()).toContain('03')
    expect(wrapper.text()).toContain('定制化音乐企划')
  })

  it('renders correctly with English content', async () => {
    i18n.global.locale.value = 'en'
    
    const wrapper = mount(ServiceCards, {
      global: {
        plugins: [i18n]
      }
    })

    await wrapper.vm.$nextTick()

    expect(wrapper.text()).toContain('Global Distribution & Operations')
    expect(wrapper.text()).toContain('Songwriter Full-Chain Services')
    expect(wrapper.text()).toContain('Artist Music Full-Service Solutions')
    expect(wrapper.text()).toContain('One-click global reach')
  })

  it('has correct structure and classes', () => {
    const wrapper = mount(ServiceCards, {
      global: {
        plugins: [i18n]
      }
    })

    expect(wrapper.find('.service-cards').exists()).toBe(true)
    expect(wrapper.find('.cards-wrapper').exists()).toBe(true)
    expect(wrapper.findAll('.service-card')).toHaveLength(3)
    expect(wrapper.findAll('.card-header')).toHaveLength(3)
    expect(wrapper.findAll('.card-title')).toHaveLength(3)
    expect(wrapper.findAll('.card-number')).toHaveLength(3)
    expect(wrapper.findAll('.features-section')).toHaveLength(3)
  })

  it('renders bullet points correctly', () => {
    const wrapper = mount(ServiceCards, {
      global: {
        plugins: [i18n]
      }
    })

    const bulletPoints = wrapper.findAll('.bullet')
    // 第一个卡片3个点，第二个卡片4个点，第三个卡片4个点
    expect(bulletPoints).toHaveLength(11) // 3 + 4 + 4 = 11
  })

  it('applies secondary class to card numbers correctly', () => {
    const wrapper = mount(ServiceCards, {
      global: {
        plugins: [i18n]
      }
    })

    const cardNumbers = wrapper.findAll('.card-number')
    expect(cardNumbers[0].classes()).not.toContain('secondary')
    expect(cardNumbers[1].classes()).toContain('secondary')
    expect(cardNumbers[2].classes()).toContain('secondary')
  })
})
