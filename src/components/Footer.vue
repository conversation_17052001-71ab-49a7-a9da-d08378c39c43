<template>
  <footer class="footer">
    <!-- 主要内容区域 -->
    <div class="footer-main">
      <div class="footer-content">
        <!-- 左侧：Logo和标语 -->
        <div class="footer-brand">
          <div class="footer-logo">
            <img src="@/assets/imgs/logo.png" alt="海之轨迹" />
          </div>
          <p class="footer-slogan">{{ $t("footer.slogan") }}</p>
        </div>

        <!-- 右侧：社交媒体 -->
        <div class="footer-links">
          <!-- 社交媒体图标 -->
          <div class="social-media">
            <a href="#" class="social-link" aria-label="抖音" @click.prevent>
              <img src="@/assets/imgs/tiktok.png" alt="抖音" />
            </a>
            <a href="#" class="social-link" aria-label="微信" @click.prevent>
              <img src="@/assets/imgs/wechat.png" alt="微信" />
            </a>
            <a href="#" class="social-link" aria-label="小红书" @click.prevent>
              <img src="@/assets/imgs/red.png" alt="小红书" />
            </a>
            <a
              href="#"
              class="social-link"
              aria-label="新浪微博"
              @click.prevent
            >
              <img src="@/assets/imgs/weibo.png" alt="新浪微博" />
            </a>
            <a
              href="#"
              class="social-link"
              aria-label="哔哩哔哩"
              @click.prevent
            >
              <img src="@/assets/imgs/bilibili.png" alt="哔哩哔哩" />
            </a>
            <a href="#" class="social-link" aria-label="YouTube" @click.prevent>
              <img src="@/assets/imgs/Youtube.png" alt="YouTube" />
            </a>
          </div>
        </div>
      </div>
      <!-- 友好链接 -->
      <div class="friendly-links">
        <span class="friendly-links-label">{{
          $t("footer.friendlyLinks")
        }}</span>
        <div class="friendly-links-list">
          <a href="#" class="friendly-link" @click.prevent>
            {{ $t("footer.links.haikuiMusic") }}
          </a>
          <a href="#" class="friendly-link" @click.prevent>
            {{ $t("footer.links.haixingMusic") }}
          </a>
          <a href="#" class="friendly-link" @click.prevent>
            {{ $t("footer.links.haimianMusic") }}
          </a>
        </div>
      </div>
    </div>

    <!-- 底部版权信息 -->
    <div class="footer-bottom">
      <div class="footer-bottom-content">
        <!-- 左侧：版权信息 -->
        <div class="copyright-info">
          <p class="copyright">{{ $t("footer.copyright") }}</p>
          <p class="icp">{{ $t("footer.icp") }}</p>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup>
// Footer 组件 - 使用 script setup 语法糖
</script>

<style lang="scss" scoped>
.footer {
  background-color: $primary-color;
  color: $text-white;
  width: 100%;
}

.footer-main {
  padding: 120px $container-padding-desktop 60px;

  @include desktop-only {
    //max-width 1199
    padding: 80px $container-padding-tablet 40px;
  }

  @include tablet-only {
    padding: 60px $container-padding-mobile 30px;
  }

  @include mobile-only {
    padding: 40px $container-padding-small 20px;
  }
}

.footer-content {
  @include flex-between;
  align-items: flex-start;
  gap: $spacing-3xl;
  max-width: $container-wide-width;
  margin: 0 auto;

  @include medium-only {
    @include flex-column-center;
    gap: $spacing-2xl;
    text-align: center;
  }

  @include tablet-only {
    @include flex-column-center;
    gap: $spacing-2xl;
    text-align: center;
  }
}

// 左侧品牌区域
.footer-brand {
  @include flex-column;
  align-items: center;
  gap: $spacing-xl;
  flex: 0 0 auto;
  @include desktop-up {
    align-items: flex-start;
  }
  @include medium-up {
    align-items: flex-start;
  }
}

.footer-logo {
  height: 200px;
  @media (max-width: 1024px) {
    height: 100px;
  }

  img {
    @include image-contain;
  }

  @include tablet-only {
    height: 120px;
  }

  @include mobile-only {
    height: 80px;
  }
}

.footer-slogan {
  @include font-primary($font-size-xl, $font-weight-light);
  letter-spacing: 0.25em;
  @include text-center;
  color: $text-white-75;
  margin: 0;

  @include tablet-only {
    font-size: $font-size-base;
  }

  @include mobile-only {
    font-size: $font-size-sm;
    letter-spacing: 0.1em;
  }
}

// 右侧链接区域
.footer-links {
  @include flex-column;
  justify-content: space-between;
  align-items: center;
  gap: $spacing-sm;
  flex: 1;
  min-height: 200px;

  @include desktop-up {
    align-items: flex-end;
    width: 100%;
  }
  @include medium-up {
    align-items: flex-end;
  }
}

// 社交媒体图标
.social-media {
  display: flex;
  align-items: center;
  gap: $spacing-2xl;
  flex-wrap: wrap;

  @include desktop-only {
    gap: $spacing-xl;
  }

  @include tablet-only {
    gap: $spacing-lg;
    justify-content: center;
  }

  @include mobile-only {
    gap: $spacing-md;
  }
}

.social-link {
  @include flex-center;
  @include transition;
  opacity: 1;

  &:hover {
    @include hover-lift(2px);
    opacity: 0.8;
  }

  img {
    width: auto;
    height: auto;
    max-width: 100%;
    max-height: 40px;
    @include image-contain;
  }
}

// 友好链接
.friendly-links {
  display: flex;
  justify-content: flex-end;
  gap: $spacing-md;
  max-width: $container-wide-width;
  margin: 120px auto 60px;

  @include tablet-only {
    @include flex-column;
    gap: $spacing-sm;
    align-items: center;
  }

  @include mobile-only {
    margin-top: 0;
  }
}

.friendly-links-label {
  @include font-primary($font-size-2xl, $font-weight-light);
  color: $text-white-75;
  white-space: nowrap;

  @include tablet-only {
    font-size: $font-size-lg;
  }
}

.friendly-links-list {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  flex-wrap: wrap;

  @include tablet-only {
    justify-content: center;
    gap: $spacing-sm;
  }

  @include mobile-only {
    @include flex-column;
    gap: $spacing-xs;
  }
}

.friendly-link {
  @include font-primary($font-size-xl, $font-weight-bold);
  color: $text-white;
  text-decoration: none;
  @include transition;
  white-space: nowrap;

  &:hover {
    color: $text-white-75;
    @include hover-lift(1px);
  }

  @include tablet-only {
    font-size: $font-size-base;
  }
}

// 底部版权区域
.footer-bottom {
  border-top: 1px solid $border-light;
  padding: $spacing-3xl $container-padding-desktop;

  @include desktop-only {
    padding: $spacing-2xl $container-padding-tablet;
  }

  @include tablet-only {
    padding: $spacing-xl $container-padding-mobile;
  }

  @include mobile-only {
    padding: $spacing-lg $container-padding-small;
  }
}

.footer-bottom-content {
  @include flex-between;
  gap: $spacing-3xl;
  max-width: $container-wide-width;
  margin: 0 auto;

  @include tablet-only {
    @include flex-column-center;
    gap: $spacing-xl;
    text-align: center;
  }
}

.copyright-info {
  @include flex-column;
  gap: $spacing-md;
}

.copyright,
.icp {
  @include font-primary($font-size-base, $font-weight-bold);
  color: $text-white-75;
  margin: 0;
}

// 响应式设计已通过 Sass 混入实现
</style>
