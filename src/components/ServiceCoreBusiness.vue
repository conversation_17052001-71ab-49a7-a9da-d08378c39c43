<template>
  <section class="service-core-business">
    <div class="container">
      <div class="content-wrapper">
        <!-- 左侧标题区域 -->
        <div class="title-section">
          <h2 class="section-title">{{ $t("services.coreBusiness.title") }}</h2>
          <div class="title-divider"></div>
          <h3
            class="section-subtitle"
            data-aos="fade-zoom-in"
            data-aos-delay="0"
          >
            {{ $t("services.coreBusiness.subtitle") }}
          </h3>
        </div>

        <!-- 右侧描述区域 -->
        <div
          class="description-section"
          data-aos="fade-zoom-in"
          data-aos-delay="500"
          data-aos-offset="0"
        >
          <p class="description-text">
            {{ $t("services.coreBusiness.description") }}
          </p>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
</script>

<style lang="scss" scoped>
.service-core-business {
  padding: 60px 0 0;
  background-color: #181818;

  .container {
    max-width: $container-max-width; //1320
    margin: 0 auto;
    background: url("../assets/imgs/services-bg-1.png") no-repeat center/cover;

    @include tablet-only {
      padding: 0 $container-padding-tablet;
    }

    @include mobile-only {
      padding: 0 $container-padding-mobile;
    }
  }

  .content-wrapper {
    display: flex;
    flex-direction: column;
    gap: 60px;
    min-height: 600px;

    @include medium-only {
      flex-direction: column;
      gap: 40px;
      min-height: auto;
    }
  }

  .title-section {
    display: flex;
    flex-direction: column;
    gap: 100px;
    flex-shrink: 0;

    @include medium-only {
      gap: 60px;
      align-items: center;
      text-align: center;
    }

    @include mobile-only {
      gap: 40px;
    }
  }

  .section-title {
    @include font-chinese(32px, $font-weight-bold);
    color: $text-white;
    line-height: 1em;
    margin: 0;

    @include tablet-only {
      font-size: 28px;
    }

    @include mobile-only {
      font-size: 24px;
    }
  }

  .title-divider {
    width: 30px;
    height: 2px;
    background-color: $text-white;

    @include medium-only {
      align-self: center;
    }
  }

  .section-subtitle {
    @include font-chinese(100px, 250);
    color: $text-white;
    line-height: 1em;

    @include desktop-only {
      font-size: 80px;
    }

    @include tablet-only {
      font-size: 60px;
    }

    @include mobile-only {
      font-size: 40px;
    }
  }

  .description-section {
    display: flex;
    justify-content: flex-end;
    margin-top: 100px;

    @include medium-only {
    }
  }

  .description-text {
    width: 660px;
    @include font-chinese(20px, $font-weight-light);
    color: $text-white;
    line-height: 2em;
    margin: 0;

    @include tablet-only {
      font-size: 18px;
      line-height: 1.8em;
    }

    @include mobile-only {
      font-size: 16px;
      line-height: 1.6em;
    }
  }
}
</style>
