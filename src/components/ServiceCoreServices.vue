<template>
  <section class="service-core-services">
    <!-- 背景装饰圆圈 -->
    <div class="background-decoration">
      <div class="circles-container">
        <div v-for="(circle, index) in backgroundCircles" :key="index" 
             :class="['circle', `circle-${index + 1}`]"
             :style="{ opacity: circle.opacity }">
        </div>
      </div>
      
      <!-- 装饰图标 -->
      <div class="decoration-icons">
        <div class="icon icon-1"></div>
        <div class="icon icon-2"></div>
        <div class="icon icon-3"></div>
        <div class="icon icon-4"></div>
        <div class="icon icon-center"></div>
      </div>
    </div>

    <div class="container">
      <div class="content-wrapper">
        <!-- 标题区域 -->
        <div class="title-section">
          <div class="title-content">
            <h2 class="section-title">{{ $t('services.coreServices.title') }}</h2>
            <h3 class="section-subtitle">{{ $t('services.coreServices.subtitle') }}</h3>
          </div>
        </div>

        <!-- 服务卡片网格 -->
        <div class="services-grid">
          <!-- 左侧主要服务卡片 -->
          <div class="main-service-card">
            <div class="card-icon primary-icon">
              <div class="icon-circles">
                <div class="outer-circle"></div>
                <div class="inner-circle"></div>
              </div>
            </div>
            <div class="card-content">
              <h4 class="card-title">{{ $tm('services.coreServices.services')[0].title }}</h4>
              <p class="card-description">{{ $tm('services.coreServices.services')[0].description }}</p>
            </div>
          </div>

          <!-- 右侧次要服务卡片 -->
          <div class="secondary-services">
            <div class="secondary-card top-card">
              <div class="card-icon secondary-icon">
                <div class="icon-design icon-design-1"></div>
              </div>
              <div class="card-content">
                <h4 class="card-title">{{ $tm('services.coreServices.services')[1].title }}</h4>
                <p class="card-description">{{ $tm('services.coreServices.services')[1].description }}</p>
              </div>
            </div>

            <div class="secondary-cards-row">
              <div class="secondary-card">
                <div class="card-icon secondary-icon">
                  <div class="icon-design icon-design-2"></div>
                </div>
                <div class="card-content">
                  <h4 class="card-title">{{ $tm('services.coreServices.services')[2].title }}</h4>
                  <p class="card-description">{{ $tm('services.coreServices.services')[2].description }}</p>
                </div>
              </div>

              <div class="secondary-card">
                <div class="card-icon secondary-icon">
                  <div class="icon-design icon-design-3"></div>
                </div>
                <div class="card-content">
                  <h4 class="card-title">{{ $tm('services.coreServices.services')[3].title }}</h4>
                  <p class="card-description">{{ $tm('services.coreServices.services')[3].description }}</p>
                </div>
              </div>

              <div class="secondary-card">
                <div class="card-icon secondary-icon">
                  <div class="icon-design icon-design-4"></div>
                </div>
                <div class="card-content">
                  <h4 class="card-title">{{ $tm('services.coreServices.services')[4].title }}</h4>
                  <p class="card-description">{{ $tm('services.coreServices.services')[4].description }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部品牌标识 -->
        <div class="brand-section">
          <div class="brand-logo">
            <div class="logo-design">
              <div class="logo-circles">
                <div v-for="n in 7" :key="n" class="logo-circle"></div>
              </div>
              <div class="logo-text">Sea Track</div>
            </div>
          </div>
          
          <div class="brand-decorations">
            <div class="decoration decoration-1"></div>
            <div class="decoration decoration-2"></div>
            <div class="decoration decoration-3"></div>
            <div class="decoration decoration-4"></div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref } from 'vue'

// 背景装饰圆圈配置
const backgroundCircles = ref([
  { opacity: 0.3 },
  { opacity: 1 },
  { opacity: 0.5 },
  { opacity: 0.5 },
  { opacity: 0.3 },
  { opacity: 0.5 },
  { opacity: 0.8 },
  { opacity: 0.8 },
  { opacity: 0.5 },
  { opacity: 0.3 },
  { opacity: 0.3 }
])
</script>

<style lang="scss" scoped>
.service-core-services {
  position: relative;
  width: 100%;
  height: 2045px;
  background-color: $bg-primary;
  overflow: hidden;
  
  @include tablet-only {
    height: auto;
    min-height: 1500px;
  }
  
  @include mobile-only {
    height: auto;
    min-height: 1200px;
  }
}

.background-decoration {
  position: absolute;
  top: 180px;
  left: 104px;
  width: 1600px;
  height: 1600px;
  pointer-events: none;
  
  @include tablet-only {
    top: 100px;
    left: 50px;
    width: 1200px;
    height: 1200px;
  }
  
  @include mobile-only {
    top: 50px;
    left: 20px;
    width: 800px;
    height: 800px;
  }
}

.circles-container {
  position: relative;
  width: 100%;
  height: 100%;
  
  .circle {
    position: absolute;
    border: 1px solid $text-light;
    border-radius: 50%;
    
    &.circle-1 {
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
    
    &.circle-2 {
      top: 200px;
      left: 200px;
      width: calc(100% - 400px);
      height: calc(100% - 400px);
    }
    
    &.circle-3 {
      top: 300px;
      left: 300px;
      width: calc(100% - 600px);
      height: calc(100% - 600px);
    }
    
    &.circle-4 {
      top: 350px;
      left: 350px;
      width: calc(100% - 700px);
      height: calc(100% - 700px);
    }
    
    &.circle-5 {
      top: 400px;
      left: 400px;
      width: calc(100% - 800px);
      height: calc(100% - 800px);
    }
    
    &.circle-6 {
      top: 450px;
      left: 450px;
      width: calc(100% - 900px);
      height: calc(100% - 900px);
    }
    
    &.circle-7 {
      top: 500px;
      left: 500px;
      width: calc(100% - 1000px);
      height: calc(100% - 1000px);
    }
    
    &.circle-8 {
      top: 500px;
      left: 500px;
      width: calc(100% - 1000px);
      height: calc(100% - 1000px);
    }
    
    &.circle-9 {
      top: 500px;
      left: 500px;
      width: calc(100% - 1000px);
      height: calc(100% - 1000px);
    }
    
    &.circle-10 {
      top: 680px;
      left: 680px;
      width: 240px;
      height: 240px;
    }
    
    &.circle-11 {
      top: 680px;
      left: 680px;
      width: 240px;
      height: 240px;
    }
  }
}

.decoration-icons {
  position: absolute;
  top: 337px;
  left: 359px;
  width: 891px;
  height: 925px;
  
  .icon {
    position: absolute;
    background-color: $text-primary;
    
    &.icon-1 {
      top: 412px;
      left: 132px;
      width: 20px;
      height: 26px;
    }
    
    &.icon-2 {
      top: 893px;
      left: 225px;
      width: 33px;
      height: 31px;
    }
    
    &.icon-3 {
      top: 500px;
      right: 0;
      width: 30px;
      height: 32px;
    }
    
    &.icon-4 {
      top: 103px;
      left: 0;
      width: 30px;
      height: 32px;
    }
    
    &.icon-center {
      top: 57px;
      left: 435px;
      width: 12px;
      height: 12px;
      border-radius: 50%;
    }
  }
}

.container {
  position: relative;
  max-width: $container-max-width;
  margin: 0 auto;
  padding: 120px 50px;
  z-index: 1;
  
  @include tablet-only {
    padding: 80px 40px;
  }
  
  @include mobile-only {
    padding: 60px 20px;
  }
}

.content-wrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 180px;
  
  @include tablet-only {
    gap: 120px;
    align-items: stretch;
  }
  
  @include mobile-only {
    gap: 80px;
  }
}

.title-section {
  align-self: stretch;
  
  .title-content {
    display: flex;
    flex-direction: column;
    gap: 30px;
  }
  
  .section-title {
    @include font-chinese(32px, $font-weight-bold);
    color: $text-primary;
    line-height: 1em;
    margin: 0;
    
    @include tablet-only {
      font-size: 28px;
    }
    
    @include mobile-only {
      font-size: 24px;
    }
  }
  
  .section-subtitle {
    @include font-chinese(100px, $font-weight-light);
    color: $text-primary;
    line-height: 1em;
    margin: 0;
    
    @include desktop-only {
      font-size: 80px;
    }
    
    @include tablet-only {
      font-size: 60px;
    }
    
    @include mobile-only {
      font-size: 40px;
    }
  }
}

.services-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  width: 100%;
  
  @include mobile-only {
    flex-direction: column;
  }
}

.main-service-card {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 20px;
  padding: 40px;
  width: 520px;
  height: 450px;
  background: linear-gradient(135deg, $text-primary 0%, #181818 100%);
  border-radius: 30px;
  
  @include tablet-only {
    width: 100%;
    max-width: 520px;
  }
  
  @include mobile-only {
    height: auto;
    min-height: 350px;
  }
}

.secondary-services {
  display: flex;
  flex-direction: column;
  gap: 20px;
  flex: 1;
  padding-top: 300px;
  
  @include tablet-only {
    padding-top: 0;
    width: 100%;
  }
}

.top-card {
  width: 780px;
  height: 300px;
  
  @include tablet-only {
    width: 100%;
  }
}

.secondary-cards-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  
  @include mobile-only {
    flex-direction: column;
  }
}

.secondary-card {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 20px;
  padding: 40px;
  background-color: $bg-primary;
  border: 1px solid $text-light;
  border-radius: 30px;
  width: 380px;
  height: 450px;
  
  @include tablet-only {
    width: calc(50% - 10px);
    min-width: 300px;
  }
  
  @include mobile-only {
    width: 100%;
    height: auto;
    min-height: 300px;
  }
  
  &.top-card {
    width: 780px;
    height: 300px;
    
    @include tablet-only {
      width: 100%;
    }
  }
}

.card-icon {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.primary-icon {
  .icon-circles {
    position: relative;
    width: 80px;
    height: 80px;
    
    .outer-circle {
      position: absolute;
      top: 0;
      left: 0;
      width: 80px;
      height: 80px;
      border: 3px solid rgba(255, 255, 255, 0.6);
      border-radius: 50%;
    }
    
    .inner-circle {
      position: absolute;
      top: 17px;
      left: 17px;
      width: 46px;
      height: 46px;
      border: 3px solid rgba(255, 255, 255, 0.6);
      border-radius: 50%;
    }
  }
}

.secondary-icon {
  .icon-design {
    width: 80px;
    height: 80px;
    background-color: $bg-primary;
    border-radius: 50%;
    position: relative;
    
    &.icon-design-1 {
      &::before {
        content: '';
        position: absolute;
        top: 0;
        right: 39px;
        width: 2px;
        height: 80px;
        background-color: $text-secondary;
      }
      
      &::after {
        content: '';
        position: absolute;
        top: 21px;
        left: 0;
        width: 80px;
        height: 2px;
        background-color: $text-secondary;
      }
    }
    
    &.icon-design-2 {
      &::before {
        content: '';
        position: absolute;
        top: 22px;
        left: 25px;
        width: 30px;
        height: 16px;
        background-color: $text-secondary;
        clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
      }
    }
    
    &.icon-design-3 {
      &::before {
        content: '';
        position: absolute;
        top: 15px;
        left: 0;
        width: 50px;
        height: 50px;
        border: 3px solid $text-secondary;
        border-radius: 50%;
      }
      
      &::after {
        content: '';
        position: absolute;
        top: 15px;
        right: 0;
        width: 50px;
        height: 50px;
        border: 3px solid $text-secondary;
        border-radius: 50%;
      }
    }
    
    &.icon-design-4 {
      &::before {
        content: '';
        position: absolute;
        top: 22px;
        left: 6px;
        width: 60px;
        height: 40px;
        background-color: $text-secondary;
        border-radius: 8px;
      }
    }
  }
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  flex: 1;
}

.card-title {
  @include font-chinese(32px, $font-weight-bold);
  line-height: 1em;
  margin: 0;
  
  @include mobile-only {
    font-size: 24px;
  }
}

.main-service-card .card-title {
  color: $text-white;
}

.secondary-card .card-title {
  color: $text-primary;
}

.card-description {
  @include font-chinese(20px, $font-weight-light);
  line-height: 1.5em;
  margin: 0;
  
  @include mobile-only {
    font-size: 16px;
  }
}

.main-service-card .card-description {
  color: rgba(255, 255, 255, 0.8);
}

.secondary-card .card-description {
  color: $text-secondary;
}

.brand-section {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  padding-top: 160px;
  width: 100%;
  
  @include tablet-only {
    justify-content: center;
    padding-top: 80px;
  }
  
  @include mobile-only {
    flex-direction: column;
    align-items: center;
    gap: 40px;
    padding-top: 60px;
  }
}

.brand-logo {
  .logo-design {
    position: relative;
    width: 220px;
    height: 220px;
    background-color: #181818;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .logo-circles {
      position: absolute;
      top: 26px;
      left: 26px;
      width: 84px;
      height: 76px;
      
      .logo-circle {
        position: absolute;
        border: 0.5px solid #181818;
        border-radius: 50%;
        background-color: $text-white;
        
        &:nth-child(1) { width: 202px; height: 202px; top: -63px; left: -59px; }
        &:nth-child(2) { width: 183px; height: 183px; top: -54px; left: -50px; }
        &:nth-child(3) { width: 165px; height: 165px; top: -45px; left: -41px; }
        &:nth-child(4) { width: 147px; height: 147px; top: -36px; left: -32px; }
        &:nth-child(5) { width: 128px; height: 128px; top: -26px; left: -22px; }
        &:nth-child(6) { width: 110px; height: 110px; top: -17px; left: -13px; }
        &:nth-child(7) { width: 92px; height: 92px; top: -8px; left: -4px; }
      }
    }
    
    .logo-text {
      @include font-chinese(20px, $font-weight-normal);
      color: $text-primary;
      position: relative;
      z-index: 2;
    }
  }
}

.brand-decorations {
  display: flex;
  gap: 20px;
  margin-left: 40px;
  
  @include mobile-only {
    margin-left: 0;
    justify-content: center;
  }
  
  .decoration {
    width: 200px;
    height: 200px;
    border: 1px solid #181818;
    
    &.decoration-1 {
      background-color: #181818;
      border-radius: 50%;
    }
    
    &.decoration-2 {
      background-color: #181818;
    }
    
    &.decoration-3 {
      background-color: $bg-primary;
      border: 1px solid $bg-primary;
    }
    
    &.decoration-4 {
      background-color: #181818;
      border-radius: 50%;
    }
  }
}

// 多语言字体优化
:deep(.service-core-services) {
  // 英文环境下的字体优化
  &[lang="en"],
  &[data-lang="en"] {
    .section-title {
      @include font-english(32px, $font-weight-bold);
      
      @include tablet-only {
        font-size: 28px;
      }
      
      @include mobile-only {
        font-size: 24px;
      }
    }
    
    .section-subtitle {
      @include font-english(100px, $font-weight-light);
      
      @include desktop-only {
        font-size: 80px;
      }
      
      @include tablet-only {
        font-size: 60px;
      }
      
      @include mobile-only {
        font-size: 40px;
      }
    }
    
    .card-title {
      @include font-english(32px, $font-weight-bold);
      
      @include mobile-only {
        font-size: 24px;
      }
    }
    
    .card-description {
      @include font-english(20px, $font-weight-light);
      line-height: 1.4em;
      
      @include mobile-only {
        font-size: 16px;
      }
    }
  }
  
  // 日文环境下的字体优化
  &[lang="ja"],
  &[data-lang="ja"] {
    .section-title {
      @include font-chinese(32px, $font-weight-bold);
      letter-spacing: 0.05em;
      
      @include tablet-only {
        font-size: 28px;
      }
      
      @include mobile-only {
        font-size: 24px;
      }
    }
    
    .section-subtitle {
      @include font-chinese(100px, $font-weight-light);
      letter-spacing: 0.02em;
      
      @include desktop-only {
        font-size: 80px;
      }
      
      @include tablet-only {
        font-size: 60px;
      }
      
      @include mobile-only {
        font-size: 40px;
      }
    }
    
    .card-title {
      @include font-chinese(32px, $font-weight-bold);
      letter-spacing: 0.02em;
      
      @include mobile-only {
        font-size: 24px;
      }
    }
    
    .card-description {
      @include font-chinese(20px, $font-weight-light);
      line-height: 1.6em;
      letter-spacing: 0.02em;
      
      @include mobile-only {
        font-size: 16px;
      }
    }
  }
  
  // 韩文环境下的字体优化
  &[lang="ko"],
  &[data-lang="ko"] {
    .section-title {
      @include font-chinese(32px, $font-weight-bold);
      letter-spacing: 0.03em;
      
      @include tablet-only {
        font-size: 28px;
      }
      
      @include mobile-only {
        font-size: 24px;
      }
    }
    
    .section-subtitle {
      @include font-chinese(100px, $font-weight-light);
      letter-spacing: 0.01em;
      
      @include desktop-only {
        font-size: 80px;
      }
      
      @include tablet-only {
        font-size: 60px;
      }
      
      @include mobile-only {
        font-size: 40px;
      }
    }
    
    .card-title {
      @include font-chinese(32px, $font-weight-bold);
      letter-spacing: 0.01em;
      
      @include mobile-only {
        font-size: 24px;
      }
    }
    
    .card-description {
      @include font-chinese(20px, $font-weight-light);
      line-height: 1.5em;
      letter-spacing: 0.01em;
      
      @include mobile-only {
        font-size: 16px;
      }
    }
  }
}
</style>
