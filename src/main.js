import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import i18n from './i18n'
import AOS from 'aos'
import 'aos/dist/aos.css'

// 导入mock数据（开发环境）
if (import.meta.env.DEV) {
  import('../mock/index.js')
}

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(i18n)
app.mount('#app')
app.use(AOS.init({
  duration: 2000,
  easing:"ease-in-out"
}));
