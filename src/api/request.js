import axios from 'axios'
import { mockData, createMockResponse, mockDelay } from '../mock/index'

// 判断是否使用mock数据
const useMock = import.meta.env.DEV || import.meta.env.VITE_USE_MOCK === 'true'

// Mock API映射表 - 支持路径匹配
const mockApiMap = {
  '/personList': () => createMockResponse({
    list: mockData.personList,
    total: mockData.personList.length
  }),
  '/news': () => createMockResponse({
    list: mockData.newsList,
    total: mockData.newsList.length
  }),
  '/banner': () => createMockResponse(mockData.banner),
  '/person': (params) => {
    // 支持搜索功能
    let results = mockData.person || [];
    // if (params && params.name) {
    //   results = results.filter(person =>
    //     person.name.toLowerCase().includes(params.name.toLowerCase()) ||
    //     (person.alias && person.alias.toLowerCase().includes(params.name.toLowerCase()))
    //   );
    // }
    return createMockResponse({
      list: results,
    });
  },
  '/detail': () => createMockResponse(mockData.detail),
  '/albums': () => createMockResponse(mockData.albums),
  '/albumsDetail': () => createMockResponse(mockData.albumsDetail),
  '/single': () => createMockResponse(mockData.single),
  '/newsList': () => createMockResponse(mockData.newsList),
  '/newsDetail': () => createMockResponse(mockData.news)
}

// 检查URL是否匹配mock API
const findMockHandler = (url) => {
  // 直接匹配
  if (mockApiMap[url]) {
    return mockApiMap[url]
  }

  // 模式匹配（支持动态路径）
  for (const pattern in mockApiMap) {
    if (url.startsWith(pattern)) {
      return mockApiMap[pattern]
    }
  }

  return null
}

// 根据环境变量确定基础URL
const getBaseURL = () => {
  if (useMock) {
    return '/api'  // mock环境
  }
  return import.meta.env.VITE_API_BASE_URL || 'https://api.example.com'
}

// 创建axios实例
const request = axios.create({
  baseURL: getBaseURL(),
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  async config => {
    console.log('发送请求:', config.url)

    // 如果使用mock数据，直接返回mock响应
    if (useMock) {
      const mockHandler = findMockHandler(config.url)
      if (mockHandler) {
        console.log('使用Mock数据:', config.url, '参数:', config.params)
        // 模拟网络延迟
        await mockDelay(300)
        // 创建一个假的响应对象，传递查询参数
        const mockResponse = mockHandler(config.params)
        // 抛出一个特殊的错误，在响应拦截器中处理
        throw { isMockResponse: true, data: mockResponse }
      }
    }

    // 可以在这里添加token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    console.log('响应数据:', response.data)

    // 统一处理响应数据格式
    if (response.data && response.data.code !== undefined) {
      if (response.data.code === 200) {
        return response.data.data
      } else {
        // 处理业务错误
        console.error('业务错误:', response.data.message)
        return Promise.reject(new Error(response.data.message))
      }
    }

    return response.data
  },
  error => {
    // 处理mock响应
    if (error.isMockResponse) {
      console.log('Mock响应数据:', error.data)
      // 统一处理mock响应数据格式
      if (error.data && error.data.code === 200) {
        return Promise.resolve(error.data.data)
      }
      return Promise.resolve(error.data)
    }

    // 对响应错误做点什么
    console.error('响应错误:', error)

    if (error.response) {
      // 服务器返回了错误状态码
      switch (error.response.status) {
        case 401:
          console.error('未授权，请重新登录')
          break
        case 403:
          console.error('拒绝访问')
          break
        case 404:
          console.error('请求的资源不存在')
          break
        case 500:
          console.error('服务器内部错误')
          break
        default:
          console.error('请求失败:', error.response.data?.message || error.message)
      }
    } else if (error.request) {
      console.error('网络错误，请检查网络连接')
    } else {
      console.error('请求配置错误:', error.message)
    }

    return Promise.reject(error)
  }
)

export default request
