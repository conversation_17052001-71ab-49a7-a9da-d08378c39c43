<template>
  <suspense>
    <div class="artist-detail-page container">
      <!-- 标题部分 -->
      <div class="title-section">
        <h1 class="title">{{ info.name }}</h1>
        <!-- 返回按钮 -->
        <div class="back-section" @click="goBack">
          <button class="back-button">
            <img src="../assets/imgs/back.png" alt="Back" />
          </button>
          <span class="back-text" v-if="type == 'artist'">
            {{ $t("artists.backToAllArtists") }}
          </span>
          <span class="back-text" v-else-if="type == 'songwriter'">
            {{ $t("artists.backToAllsongwriter") }}
          </span>
          <span class="back-text" v-else="type == 'producer'">
            {{ $t("artists.backToAllproducer") }}
          </span>
        </div>
      </div>

      <!-- 介绍信息部分 -->
      <div class="intro-section">
        <div class="intro-content" :class="{ 'slide-in': isVisible }">
          <div class="intro-text">
            <div class="agency-info">
              <div class="agency-label">{{ $t("artists.agency") }}</div>
              <div class="agency-value">{{ info.category }}</div>
            </div>
            <div class="description-text">
              <p>
                {{ info.description }}
              </p>
            </div>
          </div>
          <div class="intro-image" :class="{ 'image-animate': isVisible }">
            <img src="@/assets/3.png" alt="Artist Image" />
          </div>
        </div>
      </div>
      <!-- 成员组合部分 -->
      <section v-if="info.members && info.members.length > 0">
        <div class="title-section mt180">
          <h1 class="title">{{ $t("artists.member") }}</h1>
        </div>
        <div class="members-section">
          <div class="members-grid" :class="{ 'members-animate': isVisible }">
            <div
              v-for="(member, index) in info.members"
              :key="member.id"
              class="member-card"
              :style="{ animationDelay: `${index * 0.1}s` }"
            >
              <div class="member-image">
                <img :src="member.image" :alt="member.name" />
              </div>
              <div class="member-info">
                <div class="member-name">{{ member.name }}</div>
                <div class="member-alias">{{ member.alias }}</div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <!-- 歌曲&专辑部分 -->
      <div class="albums-section">
        <AlbumList :pid="id" />
      </div>
      <!-- MV列表组件 -->
      <div class="mvs-section">
        <MVList :mvList="info.MVList" />
      </div>
    </div>
  </suspense>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import AlbumList from "@/components/AlbumList.vue";
import MVList from "@/components/MVList.vue";
import { getPersonDetail } from "@/api/home";

const route = useRoute();
const router = useRouter();
const type = route.params.type || "artist";
const id = route.params.id;

// 动画控制
const isVisible = ref(false);
const info = ref({});

// 数据
async function fetchPersonDetail() {
  try {
    const res = await getPersonDetail({ type: type, id: id });
    info.value = res.list || res;
    console.log("数据", info.value);
  } catch (e) {
    console.log("数据错误", e);
  }
}

onMounted(() => {
  fetchPersonDetail();
});

// 返回艺人列表
const goBack = () => {
  router.push("/artists/" + type);
};

// 页面加载后触发动画
onMounted(() => {
  setTimeout(() => {
    isVisible.value = true;
  }, 300);
});
</script>

<style lang="scss" scoped>
@import "@/styles/title.scss";

.artist-detail-page {
  background: $bg-primary;
  min-height: calc(100vh - #{$nav-height-desktop});

  @include tablet-only {
    min-height: calc(100vh - #{$nav-height-mobile});
  }
}

// 介绍信息部分
.intro-section {
  padding: 60px 0;
  width: 1320px;
  max-width: 100%;
  margin: 0 auto;

  @include tablet-only {
    width: 100%;
    padding: 40px 20px;
  }
}

.intro-content {
  display: flex;
  gap: 80px;
  height: 600px;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease-out;

  &.slide-in {
    opacity: 1;
    transform: translateY(0);
  }

  @include desktop-only {
    height: 500px;
  }

  @include tablet-up {
    flex-direction: column;
    height: auto;
  }

  @include tablet-only {
    flex-direction: column;
    gap: 40px;
    height: auto;
  }

  @include medium-up {
    flex-direction: row;
    gap: 40px;
  }
}

.intro-text {
  display: flex;
  flex-direction: column;
  gap: 30px;
  height: 100%;
}

.agency-info {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 10px;
  height: 50px;
}

.agency-label {
  font-family: $font-family-chinese;
  font-weight: 700;
  font-size: 16px;
  line-height: 1em;
  color: $text-primary;
}

.agency-value {
  font-family: $font-family-chinese;
  font-weight: 400;
  font-size: 20px;
  line-height: 1em;
  color: $text-primary;
}

.description-text {
  height: 550px;
  overflow-y: auto;
  padding-right: 10px;

  @include tablet-up {
    height: auto;
  }
  @include medium-up {
    flex-direction: row;
  }
  @include desktop-only {
    height: 450px;
  }

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(134, 134, 134, 0.1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(134, 134, 134, 0.5);
    border-radius: 3px;

    &:hover {
      background: rgba(134, 134, 134, 0.7);
    }
  }

  p {
    font-family: $font-family-chinese;
    font-weight: 300;
    font-size: 20px;
    line-height: 2em;
    color: $text-light;
    margin: 0;
  }
}

.intro-image {
  width: 600px;
  height: 600px;
  flex-shrink: 0;
  border-radius: 4px;
  opacity: 0;
  transform: scale(0.9);
  transition: all 1s ease-out 0.3s;

  &.image-animate {
    opacity: 1;
    transform: scale(1);
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  @include desktop-only {
    width: 500px;
    height: 500px;
  }

  @include tablet-only {
    width: 100%;
    height: 400px;
  }
}

// 成员组合部分
.members-section {
  padding: 60px 0;
  width: 1320px;
  max-width: 100%;
  margin: 0 auto;

  @include tablet-only {
    width: 100%;
    padding: 40px 20px;
  }
}

.members-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 13px;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease-out;

  &.members-animate {
    opacity: 1;
    transform: translateY(0);
  }

  @include tablet-only {
    gap: 20px;
  }
}

.member-card {
  display: flex;
  flex-direction: column;
  gap: 20px;
  opacity: 0;
  margin-bottom: 10px;
  transform: translateY(20px);
  animation: memberSlideIn 0.6s ease-out forwards;

  @include tablet-only {
    flex: 0 0 calc(50% - 10px);
    max-width: 200px;
  }

  @media (max-width: 480px) {
    flex: 0 0 calc(50% - 10px);
    max-width: 150px;
    gap: 15px;
  }
}

.member-image {
  width: 200px;
  height: 200px;
  overflow: hidden;
  border-radius: 4px;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  &:hover img {
    transform: scale(1.05);
  }

  @media (max-width: 480px) {
    width: 150px;
    height: 150px;
  }
}

.member-info {
  display: flex;
  flex-direction: column;
  gap: 10px;
  text-align: start;
}

.member-name {
  font-family: $font-family-chinese;
  font-weight: 700;
  font-size: 20px;
  line-height: 1em;
  color: $text-primary;

  @media (max-width: 480px) {
    font-size: 18px;
  }
}

.member-alias {
  font-family: $font-family-chinese;
  font-weight: 400;
  font-size: 20px;
  line-height: 1em;
  color: $text-primary;

  @media (max-width: 480px) {
    font-size: 18px;
  }
}

// 成员卡片动画
@keyframes memberSlideIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 专辑部分
.albums-section {
  padding: 0;
  width: 1320px;
  max-width: 100%;
  margin: 0 auto;

  @include tablet-only {
    width: 100%;
  }
}

// MV部分
.mvs-section {
  padding: 0;
  width: 1320px;
  max-width: 100%;
  margin: 0 auto;

  @include tablet-only {
    width: 100%;
    padding: 40px 20px;
  }
}

// 响应式设计
// @media (max-width: 968px) {
//   .intro-section {
//     padding: 30px 16px;
//   }

//   .intro-content {
//     gap: 30px;
//   }

//   .description-text {
//     p {
//       font-size: 18px;
//       line-height: 1.8em;
//     }
//   }

//   .intro-image {
//     height: 300px;
//   }

//   .members-section {
//     padding: 30px 16px;
//   }

//   .members-grid {
//     gap: 15px;
//   }

//   .member-card {
//     gap: 15px;
//   }

//   .member-image {
//     width: 120px;
//     height: 120px;
//   }

//   .member-name,
//   .member-alias {
//     font-size: 16px;
//   }
// }
</style>
