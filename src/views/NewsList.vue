<template>
  <div class="news-list-page container">
    <!-- Title Section -->
    <div class="title-section">
      <h1 class="page-title">{{ $t("newsList.title") }}</h1>
    </div>

    <!-- Loading state -->
    <div v-if="loading" class="loading-indicator">
      <div class="loading-text">{{ $t("newsList.loading") }}</div>
    </div>

    <!-- News List -->
    <div v-else-if="newsList.length > 0" class="news-list-content">
      <div class="news-list">
        <div
          v-for="(news, index) in newsList"
          :key="news.id"
          class="news-item"
          :class="{ 'news-item-last': index === newsList.length - 1 }"
          @click="viewNewsDetail(news.id)"
        >
          <!-- Left side: Date and Image -->
          <div class="news-left">
            <div class="news-date">{{ news.date }}</div>
            <div class="news-image">
              <img :src="news.cover" :alt="news.title" />
            </div>
          </div>

          <!-- Right side: Content -->
          <div class="news-right">
            <div class="news-content">
              <h3 class="news-title">{{ news.title }}</h3>
              <p class="news-description">{{ news.content }}</p>
            </div>
            <div class="news-action">
              <span class="view-link">{{ $t("newsList.viewDetail") }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Pagination -->
      <div class="pagination-section">
        <div class="pagination">
          <!-- Previous Button -->
          <div
            class="pagination-arrow"
            :class="{ disabled: currentPage === 1 }"
            @click="goToPreviousPage"
          >
            <div class="arrow-button">
              <div class="arrow-icon arrow-left">
                <div class="arrow-line"></div>
                <div class="arrow-head-left"></div>
                <div class="arrow-head-right"></div>
                <div class="arrow-head-bottom"></div>
              </div>
            </div>
          </div>

          <!-- Page Numbers -->
          <div class="page-numbers">
            <span
              v-for="page in visiblePages"
              :key="page"
              class="page-number"
              :class="{ active: page === currentPage }"
              @click="goToPage(page)"
            >
              {{ page }}
            </span>
          </div>

          <!-- Next Button -->
          <div
            class="pagination-arrow"
            :class="{ disabled: currentPage === totalPages }"
            @click="goToNextPage"
          >
            <div class="arrow-button">
              <div class="arrow-icon arrow-right">
                <div class="arrow-line"></div>
                <div class="arrow-head-left"></div>
                <div class="arrow-head-right"></div>
                <div class="arrow-head-bottom"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- No Data state -->
    <div v-else class="no-data-state">
      <RotatingRecord :duration="4" />
      <div class="no-data-content">
        <div class="no-data-text">{{ $t("newsList.noData") }}</div>
        <div class="no-data-description">
          {{ $t("newsList.noDataDescription") }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { useRouter } from "vue-router";
import { getNewsList } from "@/api/home";
import RotatingRecord from "@/components/RotatingRecord.vue";

const router = useRouter();

// Reactive data
const loading = ref(true);
const newsList = ref([]);
const currentPage = ref(1);
const pageSize = ref(5);
const totalItems = ref(0);

// Computed properties
const totalPages = computed(() => Math.ceil(totalItems.value / pageSize.value));

const visiblePages = computed(() => {
  const pages = [];
  const maxVisible = 4;
  const start = Math.max(1, currentPage.value - Math.floor(maxVisible / 2));
  const end = Math.min(totalPages.value, start + maxVisible - 1);

  for (let i = start; i <= end; i++) {
    pages.push(i);
  }
  return pages;
});

// Methods
async function fetchNewsList() {
  try {
    loading.value = true;
    const res = await getNewsList({
      page: currentPage.value,
      pageSize: pageSize.value,
    });
    console.log("新闻列表数据", res);

    if (res && res.list) {
      newsList.value = res.list;
      totalItems.value = res.total || res.list.length;
    } else {
      newsList.value = res || [];
      totalItems.value = newsList.value.length;
    }
  } catch (error) {
    console.error("新闻列表加载错误", error);
    newsList.value = [];
  } finally {
    loading.value = false;
  }
}

const viewNewsDetail = (newsId) => {
  router.push(`/news/${newsId}`);
};

const goToPage = (page) => {
  if (page !== currentPage.value && page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
    fetchNewsList();
  }
};

const goToPreviousPage = () => {
  if (currentPage.value > 1) {
    goToPage(currentPage.value - 1);
  }
};

const goToNextPage = () => {
  if (currentPage.value < totalPages.value) {
    goToPage(currentPage.value + 1);
  }
};

// Lifecycle
onMounted(() => {
  fetchNewsList();
});
</script>

<style lang="scss" scoped>
.news-list-page {
  background: $bg-primary;
  min-height: 100vh;
}

// Title Section
.title-section {
  margin: $spacing-3xl 0;

  @include tablet-only {
    margin: $spacing-2xl 0;
  }

  @include mobile-only {
    padding: 0 $container-padding-small;
  }
}

.page-title {
  @include font-chinese(100px, $font-weight-light);
  color: $text-primary;
  line-height: 1em;
  margin: 0;

  @include desktop-only {
    font-size: 60px;
  }

  @include mobile-only {
    font-size: 40px;
  }
}

// Loading and No Data states
.loading-indicator,
.no-data-state {
  @include flex-center;
  @include flex-column;
  gap: $spacing-xl;
  min-height: 50vh;

  .loading-text,
  .no-data-text {
    @include font-chinese($font-size-lg, $font-weight-normal);
    color: $text-secondary;
    text-align: center;
  }
}

.no-data-content {
  @include flex-column;
  align-items: center;
  gap: $spacing-md;
  text-align: center;
}

.no-data-description {
  @include font-chinese($font-size-base, $font-weight-light);
  color: $text-light;
  max-width: 400px;
  line-height: 1.6;

  @include mobile-only {
    font-size: $font-size-sm;
    max-width: 300px;
  }
}

// News List Content
.news-list-content {
  max-width: $container-max-width;
  margin: 0 auto;

  @include mobile-only {
    padding: 0 $container-padding-small;
  }
}

.news-list {
  @include flex-column;
  gap: $spacing-4xl;
  margin-bottom: $spacing-4xl;

  @include tablet-only {
    gap: $spacing-3xl;
    margin-bottom: $spacing-3xl;
  }
}

// News Item
.news-item {
  @include flex-between;
  align-items: flex-start;
  gap: $spacing-4xl;
  padding-bottom: $spacing-4xl;
  border-bottom: 1px solid #b7b7b7;
  cursor: pointer;
  @include transition;

  &:hover {
    opacity: 0.8;
  }

  &.news-item-last {
    border-bottom: none;
    padding-bottom: 0;
  }
  @include desktop-only {
    gap: $spacing-2xl;
  }
  @include tablet-only {
    @include flex-column;
    gap: $spacing-lg;
    padding-bottom: $spacing-2xl;
  }
}

// News Left Side
.news-left {
  @include flex-between;
  align-items: flex-start;
  gap: 200px;
  flex-shrink: 0;

  @include desktop-only {
    gap: 100px;
  }

  @include medium-only {
    gap: $spacing-xl;
  }

  @include mobile-only {
    @include flex-column;
    gap: $spacing-lg;
    align-items: flex-start;
  }
}

.news-date {
  @include font-chinese(32px, $font-weight-bold);
  color: $text-primary;
  line-height: 1em;

  @include desktop-only {
    font-size: 28px;
  }

  @include medium-only {
    font-size: 26px;
  }

  @include mobile-only {
    font-size: 24px;
  }
}

.news-image {
  width: 320px;
  height: 240px;
  border-radius: 0;
  overflow: hidden;
  flex-shrink: 0;

  @include desktop-only {
    width: 280px;
    height: 210px;
  }
  @include medium-only {
    width: 240px;
    height: 180px;
  }

  @include mobile-only {
    width: 100%;
    height: auto;
  }

  img {
    @include image-cover;
  }
}

// News Right Side
.news-right {
  height: 240px;
  @include flex-column;
  justify-content: space-between;

  @include desktop-only {
    height: 210px;
  }

  @include medium-only {
    height: 180px;
  }

  @include mobile-only {
    height: auto;
    gap: $spacing-lg;
  }
}

.news-content {
  @include flex-column;
  gap: $spacing-xl;

  @include tablet-only {
    gap: $spacing-lg;
  }
}

.news-title {
  width: 620px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  @include font-chinese(32px, $font-weight-bold);
  color: $text-primary;
  line-height: 1em;
  margin: 0;

  @include desktop-only {
    width: 360px;
    font-size: 28px;
  }

  @include medium-only {
    width: 300px;
    font-size: 24px;
  }

  @include mobile-only {
    width: 360px;
    font-size: 24px;
  }
}

.news-description {
  @include font-chinese($font-size-xl, $font-weight-light);
  color: $text-light;
  line-height: 1.5em;
  margin: 0;
  overflow: hidden;
  display: -webkit-box; //将对象作为弹性伸缩盒子模型显示;
  text-overflow: ellipsis; //溢出部分用省略号代替
  -webkit-line-clamp: 3; //设置文本显示两行
  -webkit-box-orient: vertical; //从上到下排列子元素;
  white-space: normal;

  @include medium-only {
    font-size: $font-size-lg;
  }

  @include mobile-only {
    font-size: $font-size-base;
  }
}

.news-action {
  @include tablet-only {
    align-self: flex-start;
  }
}

.view-link {
  @include font-chinese($font-size-xl, $font-weight-normal);
  color: $text-primary;
  line-height: 1em;
  cursor: pointer;
  @include transition;

  &:hover {
    opacity: 0.7;
  }
}

// Pagination Section
.pagination-section {
  @include flex-center;
  margin: $spacing-4xl 0;

  @include tablet-only {
    margin: $spacing-3xl 0;
  }
}

.pagination {
  @include flex-center;
  gap: $spacing-2xl;

  @include tablet-only {
    gap: $spacing-lg;
  }
}

.pagination-arrow {
  @include flex-center;
  gap: $spacing-xs;
  cursor: pointer;
  @include transition;

  &:hover:not(.disabled) {
    opacity: 0.7;
  }

  &.disabled {
    opacity: 0.15;
    cursor: not-allowed;
  }
}

.arrow-button {
  width: 28px;
  height: 28px;
  position: relative;
  @include flex-center;
}

.arrow-icon {
  position: relative;
  width: 100%;
  height: 100%;
}

.arrow-line {
  position: absolute;
  top: 50%;
  width: 15px;
  height: 0;
  border-top: 1.5px solid $text-primary;
  transform: translateY(-50%);
}

.arrow-left .arrow-line {
  left: 6px;
}

.arrow-right .arrow-line {
  right: 6px;
}

.arrow-head-left,
.arrow-head-right,
.arrow-head-bottom {
  position: absolute;
  background: $text-primary;
}

.arrow-left .arrow-head-left {
  top: 6.93px;
  left: 6.43px;
  width: 14.14px;
  height: 14.14px;
  transform: rotate(45deg);
  border: 1.5px solid $text-primary;
  border-right: none;
  border-bottom: none;
}

.arrow-right .arrow-head-left {
  top: 6.93px;
  right: 6.43px;
  width: 14.14px;
  height: 14.14px;
  transform: rotate(-135deg);
  border: 1.5px solid $text-primary;
  border-right: none;
  border-bottom: none;
}

.arrow-left .arrow-head-right {
  top: 6px;
  right: 1px;
  width: 3px;
  height: 16px;
  background: $text-primary;
}

.arrow-right .arrow-head-right {
  top: 6px;
  left: 1px;
  width: 3px;
  height: 16px;
  background: $text-primary;
}

.arrow-left .arrow-head-bottom {
  bottom: 6px;
  left: 1px;
  width: 3px;
  height: 16px;
  background: $text-primary;
}

.arrow-right .arrow-head-bottom {
  bottom: 6px;
  right: 1px;
  width: 3px;
  height: 16px;
  background: $text-primary;
}

.page-numbers {
  @include flex-center;
  gap: $spacing-2xl;

  @include tablet-only {
    gap: $spacing-lg;
  }
}

.page-number {
  @include font-chinese($font-size-xl, $font-weight-normal);
  color: $text-light;
  line-height: 1em;
  cursor: pointer;
  @include transition;

  &:hover {
    color: $text-primary;
  }

  &.active {
    color: $text-primary;
  }
}

// Responsive adjustments
@include mobile-only {
  .news-list-content {
    padding-top: $spacing-lg;
  }

  .title-section {
    margin: $spacing-lg 0;
  }

  .news-list {
    gap: $spacing-2xl;
    margin-bottom: $spacing-2xl;
  }

  .pagination-section {
    margin: $spacing-2xl 0;
  }

  .pagination {
    gap: $spacing-sm;
  }

  .page-numbers {
    gap: $spacing-sm;
  }
}
</style>
