<template>
  <div class="news-detail-page container">
    <!-- 标题区域 -->
    <div class="title-section">
      <div class="title-content">
        <h1 class="page-title">{{ $t("newsList.title") }}</h1>
      </div>
      <div class="back-section" @click="goBack">
        <div class="back-button">
          <img src="../assets/imgs/back.png" />
        </div>
        <span class="back-text">{{ $t("newsDetail.backToNews") }}</span>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-indicator">
      <div class="loading-text">{{ $t("newsDetail.loading") }}</div>
    </div>

    <!-- 新闻不存在 -->
    <div v-else-if="!newsDetail" class="not-found-state">
      <RotatingRecord :duration="4" />
      <div class="not-found-content">
        <div class="not-found-text">{{ $t("newsDetail.notFound") }}</div>
        <div class="not-found-description">
          {{ $t("newsDetail.notFoundDescription") }}
        </div>
      </div>
    </div>

    <!-- 新闻详情内容 -->
    <div v-else class="news-detail-content container">
      <!-- 新闻标题和日期 -->
      <div class="news-header">
        <h2 class="news-title">{{ newsDetail.title }}</h2>
        <div class="news-date">{{ newsDetail.date }}</div>
      </div>

      <!-- 新闻主体内容 -->
      <div class="news-body">
        <!-- 新闻图片 -->
        <div class="news-image" v-if="newsDetail.cover">
          <img :src="newsDetail.cover" :alt="newsDetail.title" />
        </div>

        <!-- 新闻正文 -->
        <div class="news-content">
          <p class="news-text">{{ newsDetail.content }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { getNewsDetail } from "@/api/home";
import RotatingRecord from "@/components/RotatingRecord.vue";

const route = useRoute();
const router = useRouter();

// 响应式数据
const loading = ref(true);
const newsDetail = ref(null);

// 获取新闻详情
async function fetchNewsDetail() {
  try {
    loading.value = true;
    const newsId = route.params.id;
    const res = await getNewsDetail({ id: newsId });
    console.log("新闻详情数据", res);

    if (res && res.code === 200) {
      newsDetail.value = res.data;
    } else {
      newsDetail.value = null;
    }
  } catch (error) {
    console.error("新闻详情加载错误", error);
    newsDetail.value = null;
  } finally {
    loading.value = false;
  }
}

// 返回新闻列表
function goBack() {
  router.push("/news");
}

// 生命周期
onMounted(() => {
  fetchNewsDetail();
});
</script>

<style lang="scss" scoped>
.news-detail-page {
  background: $bg-primary;
  min-height: 100vh;
}

// 标题区域
.title-section {
  @include flex-between;
  align-items: flex-end;
  gap: 10px;
  padding: 0 0 60px;
  border-bottom: 1px solid #868686;
  margin-top: 148px;

  @include desktop-only {
    margin-top: 100px;
    padding: 0 0 40px;
  }

  @include tablet-only {
    margin-top: 80px;
    padding: 0 0 30px;
  }

  @include mobile-only {
    margin-top: 60px;
    padding: 0 0 20px;
    @include flex-column;
    align-items: flex-start;
    gap: 20px;
  }
}

.title-content {
  @include flex-column;
  justify-content: flex-end;
  gap: 20px;
}

.page-title {
  @include font-chinese(100px, $font-weight-light);
  color: $text-primary;
  line-height: 1em;
  margin: 0;

  @include desktop-only {
    font-size: 80px; // 根据要求，desktop-only尺寸使用80px
  }

  @include tablet-only {
    font-size: 60px;
  }

  @include mobile-only {
    font-size: 40px;
  }
}
.back-section {
  @include flex-center;
  gap: 5px;
  cursor: pointer;

  @include transition;
  &:hover {
    opacity: 0.7;
  }

  @include mobile-only {
    width: 100%;
    justify-content: flex-end;
  }
}
// 返回按钮
.back-button {
  width: 28px;
  height: 28px;

  img {
    @include image-cover;
  }
}

.back-text {
  @include font-chinese($font-size-xl, $font-weight-normal);
  color: $text-primary;
  line-height: 1em;
}

// 加载和错误状态
.loading-indicator,
.not-found-state {
  @include flex-center;
  @include flex-column;
  gap: $spacing-xl;
  min-height: 50vh;

  .loading-text,
  .not-found-text {
    @include font-chinese($font-size-lg, $font-weight-normal);
    color: $text-secondary;
    text-align: center;
  }
}

.not-found-content {
  @include flex-column;
  align-items: center;
  gap: $spacing-md;
  text-align: center;
}

.not-found-description {
  @include font-chinese($font-size-base, $font-weight-light);
  color: $text-light;
  max-width: 400px;
  line-height: 1.6;

  @include mobile-only {
    font-size: $font-size-sm;
    max-width: 300px;
  }
}

// 新闻详情内容
.news-detail-content {
  margin-top: 80px;
  margin-bottom: 180px;

  @include desktop-only {
    margin-top: 60px;
    margin-bottom: 120px;
  }

  @include tablet-only {
    margin-top: 40px;
    margin-bottom: 100px;
  }

  @include mobile-only {
    margin-top: 20px;
    margin-bottom: 60px;
  }
}

// 新闻头部
.news-header {
  @include flex-column;
  gap: 20px;
  margin-bottom: 80px;

  @include desktop-only {
    margin-bottom: 60px;
  }

  @include tablet-only {
    margin-bottom: 40px;
  }

  @include mobile-only {
    margin-bottom: 30px;
  }
}

.news-title {
  @include font-chinese(48px, $font-weight-bold);
  color: $text-primary;
  line-height: 1.2em;
  margin: 0;

  @include desktop-only {
    font-size: 40px;
  }

  @include tablet-only {
    font-size: 36px;
  }

  @include mobile-only {
    font-size: 28px;
  }
}

.news-date {
  @include font-chinese($font-size-xl, $font-weight-normal);
  color: $text-light;
  line-height: 1em;

  @include tablet-only {
    font-size: $font-size-lg;
  }

  @include mobile-only {
    font-size: $font-size-base;
  }
}

// 新闻主体
.news-body {
  @include flex-column;
  align-items: center;
}

.news-image {
  width: 100%;
  max-width: 1320px;
  height: 740px;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 80px;

  @include desktop-only {
    height: auto;
    margin-bottom: 60px;
  }

  @include tablet-only {
    height: 400px;
    margin-bottom: 40px;
  }

  @include mobile-only {
    height: 240px;
    margin-bottom: 30px;
  }

  img {
    @include image-cover;
  }
}

.news-content {
  width: 100%;
  max-width: 920px; // 内容区域最大宽度，居中显示

  @include desktop-only {
    padding: 0 10px;
  }

  @include tablet-only {
    padding: 0 60px;
  }

  @include mobile-only {
    padding: 0 20px;
  }
}

.news-text {
  @include font-chinese($font-size-xl, $font-weight-light);
  color: $text-light;
  line-height: 2em;
  margin: 0;

  @include tablet-only {
    font-size: $font-size-lg;
    line-height: 1.8em;
  }

  @include mobile-only {
    font-size: $font-size-base;
    line-height: 1.6em;
  }
}
</style>
