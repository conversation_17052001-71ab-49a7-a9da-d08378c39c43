<template>
  <div class="service-demo-page">
    <!-- 页面标题 -->
    <section class="demo-header">
      <div class="container">
        <h1 class="demo-title">服务组件演示 / Service Component Demo</h1>
        <p class="demo-description">
          展示服务核心业务组件在不同语言下的表现效果
        </p>
        
        <!-- 语言切换器 -->
        <div class="language-switcher">
          <button 
            v-for="lang in languages" 
            :key="lang.code"
            @click="switchLanguage(lang.code)"
            :class="['lang-btn', { active: currentLocale === lang.code }]"
          >
            {{ lang.name }}
          </button>
        </div>
      </div>
    </section>
    
    <!-- 服务核心业务组件演示 -->
    <ServiceCoreBusiness />
    
    <!-- 组件信息 -->
    <section class="component-info">
      <div class="container">
        <h2>组件特性 / Component Features</h2>
        <ul class="features-list">
          <li>✅ 完全响应式设计，适配桌面、平板、手机</li>
          <li>✅ 支持中文、英文、日文、韩文四种语言</li>
          <li>✅ 基于Figma设计图精确还原</li>
          <li>✅ 使用Vue 3 Composition API</li>
          <li>✅ 集成Vue I18n国际化</li>
          <li>✅ SCSS模块化样式</li>
          <li>✅ 字体优化和多语言排版</li>
        </ul>
        
        <div class="design-specs">
          <h3>设计规格 / Design Specifications</h3>
          <div class="specs-grid">
            <div class="spec-item">
              <strong>容器宽度:</strong> 1320px (最大)
            </div>
            <div class="spec-item">
              <strong>内容高度:</strong> 600px (最小)
            </div>
            <div class="spec-item">
              <strong>标题字体:</strong> 32px / Bold
            </div>
            <div class="spec-item">
              <strong>副标题字体:</strong> 100px / Light (250)
            </div>
            <div class="spec-item">
              <strong>描述字体:</strong> 20px / Light (300)
            </div>
            <div class="spec-item">
              <strong>分隔线:</strong> 30px × 2px
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import ServiceCoreBusiness from '@/components/ServiceCoreBusiness.vue'

const { locale } = useI18n()

const currentLocale = computed(() => locale.value)

const languages = ref([
  { code: 'zh', name: '中文' },
  { code: 'en', name: 'English' },
  { code: 'ja', name: '日本語' },
  { code: 'ko', name: '한국어' }
])

const switchLanguage = (langCode) => {
  locale.value = langCode
  localStorage.setItem('locale', langCode)
}
</script>

<style lang="scss" scoped>
.service-demo-page {
  min-height: 100vh;
  background-color: $bg-primary;
}

.demo-header {
  padding: 60px 0 40px;
  text-align: center;
  
  .container {
    max-width: $container-max-width;
    margin: 0 auto;
    padding: 0 $container-padding-desktop;
    
    @include tablet-only {
      padding: 0 $container-padding-tablet;
    }
    
    @include mobile-only {
      padding: 0 $container-padding-mobile;
    }
  }
}

.demo-title {
  @include font-chinese(36px, $font-weight-bold);
  color: $text-primary;
  margin: 0 0 20px;
  
  @include tablet-only {
    font-size: 28px;
  }
  
  @include mobile-only {
    font-size: 24px;
  }
}

.demo-description {
  @include font-chinese(18px, $font-weight-normal);
  color: $text-secondary;
  margin: 0 0 40px;
  
  @include mobile-only {
    font-size: 16px;
  }
}

.language-switcher {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-bottom: 20px;
  
  @include mobile-only {
    flex-wrap: wrap;
    gap: 10px;
  }
}

.lang-btn {
  padding: 10px 20px;
  border: 2px solid $text-primary;
  background: transparent;
  color: $text-primary;
  @include font-chinese(14px, $font-weight-medium);
  cursor: pointer;
  border-radius: $border-radius-md;
  transition: all $transition-normal;
  
  &:hover {
    background-color: $text-primary;
    color: $bg-primary;
  }
  
  &.active {
    background-color: $text-primary;
    color: $bg-primary;
  }
  
  @include mobile-only {
    padding: 8px 16px;
    font-size: 12px;
  }
}

.component-info {
  padding: 60px 0;
  background-color: rgba(255, 255, 255, 0.5);
  
  .container {
    max-width: $container-max-width;
    margin: 0 auto;
    padding: 0 $container-padding-desktop;
    
    @include tablet-only {
      padding: 0 $container-padding-tablet;
    }
    
    @include mobile-only {
      padding: 0 $container-padding-mobile;
    }
  }
  
  h2 {
    @include font-chinese(28px, $font-weight-bold);
    color: $text-primary;
    margin: 0 0 30px;
    text-align: center;
    
    @include mobile-only {
      font-size: 24px;
    }
  }
  
  h3 {
    @include font-chinese(24px, $font-weight-bold);
    color: $text-primary;
    margin: 40px 0 20px;
    
    @include mobile-only {
      font-size: 20px;
    }
  }
}

.features-list {
  list-style: none;
  padding: 0;
  margin: 0 0 40px;
  
  li {
    @include font-chinese(16px, $font-weight-normal);
    color: $text-primary;
    padding: 8px 0;
    
    @include mobile-only {
      font-size: 14px;
    }
  }
}

.specs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  
  @include mobile-only {
    grid-template-columns: 1fr;
    gap: 15px;
  }
}

.spec-item {
  @include font-chinese(14px, $font-weight-normal);
  color: $text-primary;
  padding: 15px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: $border-radius-md;
  
  strong {
    font-weight: $font-weight-medium;
  }
}
</style>
