<template>
  <div class="services-page">
    <!-- 页面标题区域 -->
    <section class="hero-section">
      <div class="container">
        <h1 class="page-title">{{ $t("services.title") }}</h1>
      </div>
    </section>

    <!-- 核心业务区域 -->
    <ServiceCoreBusiness />

    <!-- 服务卡片区域 -->
    <ServiceCards />

    <!-- 核心服务区域 -->
    <ServiceCoreServices />
  </div>
</template>

<script setup>
import ServiceCoreBusiness from "@/components/ServiceCoreBusiness.vue";
import ServiceCards from "@/components/ServiceCards.vue";
import ServiceCoreServices from "@/components/ServiceCoreServices.vue";
</script>

<style lang="scss" scoped>
.hero-section {
  @include flex-center;
  .container {
    margin-bottom: 60px;
  }
}

.page-title {
  @include font-chinese(100px, $font-weight-light);
  color: $text-primary;
  line-height: 1em;
  margin: 0;

  @include desktop-only {
    font-size: 70px;
  }

  @include tablet-only {
    font-size: 50px;
  }

  @include mobile-only {
    font-size: 40px;
  }
}
</style>
