// ===== 颜色变量 =====
// 主色调
$primary-color: #313131;
$primary-light: #636363;
$primary-dark: #000000;

// 背景色
$bg-primary: #F3F1F4;
$bg-white: #F3F1F4;
$bg-transparent: transparent;

// 文字颜色
$text-primary: #313131;
$text-secondary: #636363;
$text-light: #868686;
$text-white: #FFFFFF;
$text-white-75: rgba(255, 255, 255, 0.75);

// 边框颜色
$border-light: rgba(255, 255, 255, 0.2);

// 阴影颜色
$shadow-light: rgba(0, 0, 0, 0.1);
$shadow-medium: rgba(0, 0, 0, 0.15);
$shadow-dark: rgba(0, 0, 0, 0.2);

// ===== 字体变量 =====
// 注意：具体的字体定义在 fonts.scss 中
// 中文字体 - Alibaba PuHuiTi 2.0
$font-family-chinese: 'Alibaba PuHuiTi 2.0', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;

// 英文字体 - Inter Variable Font
$font-family-english: 'Inter', 'Helvetica Neue', 'Arial', sans-serif;

// 主要字体 - 中文优先
$font-family-primary: 'Alibaba PuHuiTi 2.0', 'Inter', 'PingFang SC', 'Helvetica Neue', sans-serif;

// 次要字体 - 英文优先
$font-family-secondary: 'Inter', 'Alibaba PuHuiTi 2.0', 'Helvetica Neue', 'PingFang SC', sans-serif;

// 默认字体 - Alibaba PuHuiTi 2.0
$font-family-default: 'Alibaba PuHuiTi 2.0', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;

// 字体大小
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-base: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-2xl: 24px;
$font-size-3xl: 32px;
$font-size-4xl: 48px;
$font-size-5xl: 60px;
$font-size-6xl: 80px;
$font-size-7xl: 100px;

// 字体粗细 - 支持 Alibaba PuHuiTi 2.0 和 Inter Variable Font
$font-weight-thin: 200;       // Thin (Inter: 200, PuHuiTi: 使用 Thin)
$font-weight-light: 300;      // Light (Inter: 300, PuHuiTi: Light)
$font-weight-normal: 400;     // Regular (Inter: 400, PuHuiTi: Regular)
$font-weight-medium: 500;     // Medium (Inter: 500, PuHuiTi: Medium)
$font-weight-semibold: 600;   // SemiBold (Inter: 600, PuHuiTi: 使用 Medium fallback)
$font-weight-bold: 700;       // Bold (Inter: 700, PuHuiTi: Bold)

// Inter Variable Font 扩展字重
$font-weight-extra-light: 200;  // ExtraLight
$font-weight-extra-bold: 800;   // ExtraBold
$font-weight-black: 900;        // Black

// ===== 间距变量 =====
$spacing-xs: 5px;
$spacing-sm: 10px;
$spacing-md: 15px;
$spacing-lg: 20px;
$spacing-xl: 30px;
$spacing-2xl: 40px;
$spacing-3xl: 60px;
$spacing-4xl: 80px;

// ===== 容器变量 =====
$container-max-width: 1320px;
$container-narrow-width: 960px;
$container-wide-width: 1600px;

// 容器内边距
$container-padding-desktop: 60px;
$container-padding-tablet: 60px;
$container-padding-40: 40px;
$container-padding-mobile: 20px;
$container-padding-small: 16px;

// ===== 断点变量 =====
$breakpoint-xs: 480px;
$breakpoint-sm: 768px;
$breakpoint-md: 1024px;
$breakpoint-lg: 1280px;
$breakpoint-xl: 1440px;

// ===== 圆角变量 =====
$border-radius-sm: 4px;
$border-radius-md: 8px;
$border-radius-lg: 12px;
$border-radius-xl: 16px;

// ===== 过渡变量 =====
$transition-fast: 0.2s;
$transition-normal: 0.3s;
$transition-slow: 0.5s;
$transition-slower: 0.6s;

// 缓动函数
$ease-out-back: cubic-bezier(0.68, -0.55, 0.265, 1.55);
$ease-out-quart: cubic-bezier(0.25, 1, 0.5, 1);
$ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

// ===== Z-index 变量 =====
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;

// ===== 导航变量 =====
$nav-height-desktop: 92px;
$nav-height-mobile: 72px;

// ===== 艺人页面变量 =====
$artist-sidebar-width: 330px;
$artist-sidebar-width-tablet: 280px;
$artist-sidebar-height: 600px;
$artist-sidebar-height-tablet: 500px;
$artist-sidebar-height-mobile: 300px;

$artist-image-size: 600px;
$artist-image-size-tablet: 500px;
$artist-image-size-medium: 400px;
$artist-image-size-mobile: 300px;

$artist-card-image-height: 320px;
$artist-card-image-height-tablet: 280px;
$artist-card-image-height-mobile: 280px;

// ===== 网格变量 =====
$grid-gap-large: 30px;
$grid-gap-medium: 20px;
$grid-gap-small: 15px;
$grid-gap-xs: 13px;
$grid-gap-tiny: 10px;
