# Sass 样式系统文档

## 项目结构

```
src/styles/
├── main.scss          # 主样式文件，导入所有其他文件
├── variables.scss     # Sass 变量定义
├── mixins.scss        # Sass 混入定义
├── container.scss     # 容器样式
└── README.md          # 本文档
```

## 配置说明

### Vite 配置
项目已配置 Sass 预处理器，支持：
- 全局变量和混入自动导入
- 现代 Sass API
- 嵌套语法
- 变量插值

### 使用方式
在 Vue 组件中使用 Sass：

```vue
<style lang="scss" scoped>
.component {
  color: $primary-color;
  @include flex-center;

  &:hover {
    @include hover-lift;
  }

  @include tablet-only {
    font-size: $font-size-sm;
  }
}
</style>
```

## 变量系统

### 颜色变量
```scss
// 主色调
$primary-color: #313131;
$primary-light: #636363;
$text-primary: #313131;
$text-secondary: #636363;
$bg-primary: #F3F1F4;
```

### 字体变量
```scss
$font-family-primary: "Alibaba PuHuiTi 2.0", sans-serif;
$font-size-base: 16px;
$font-size-lg: 18px;
$font-weight-normal: 400;
$font-weight-bold: 700;
```

### 间距变量
```scss
$spacing-sm: 10px;
$spacing-md: 15px;
$spacing-lg: 20px;
$spacing-xl: 30px;
$spacing-3xl: 60px;
```

### 断点变量
```scss
$breakpoint-xs: 480px;   // 小屏手机
$breakpoint-sm: 768px;   // 平板
$breakpoint-md: 968px;   // 中等屏幕
$breakpoint-lg: 1200px;  // 桌面

```

## 混入系统

### 响应式混入
```scss
@include mobile-only { /* 样式 */ }     // ≤479px
@include tablet-only { /* 样式 */ }     // ≤767px
@include medium-only { /* 样式 */ }     // ≤967px
@include desktop-only { /* 样式 */ }    // ≤1199px

@include mobile-up { /* 样式 */ }       // ≥480px
@include tablet-up { /* 样式 */ }       // ≥768px
@include desktop-up { /* 样式 */ }      // ≥1200px
```

### 布局混入
```scss
@include flex-center;        // 居中对齐
@include flex-between;       // 两端对齐
@include flex-column;        // 垂直布局
@include container;          // 标准容器
@include grid-responsive;    // 响应式网格
```

### 字体混入
```scss
@include font-primary($font-size-lg, $font-weight-bold);
@include font-secondary($font-size-base);
@include text-center;
@include text-truncate;
```

### 动画混入
```scss
@include transition;         // 标准过渡
@include transition-bounce;  // 弹跳过渡
@include hover-lift;         // 悬停上升
@include hover-scale;        // 悬停缩放
```

## 容器样式

### 基本容器类
```scss
.container        // 1320px 标准容器
.container-narrow // 960px 窄容器
.container-wide   // 1600px 宽容器
.container-fluid  // 100% 流体容器
```

### 使用示例
```vue
<template>
  <div class="page">
    <header class="header container">
      <h1>标题</h1>
    </header>
    <main class="content container-narrow">
      <p>内容</p>
    </main>
  </div>
</template>

<style lang="scss" scoped>
.page {
  background: $bg-primary;
}

.header {
  @include flex-between;
  padding: $spacing-lg 0;
}

.content {
  @include font-secondary;

  @include tablet-only {
    padding: $spacing-md 0;
  }
}
</style>
```

## 工具类

### 布局工具类
```scss
.flex-center         // 居中对齐
.flex-between        // 两端对齐
.flex-column         // 垂直布局
.text-center         // 文字居中
```

### 响应式显示/隐藏
```scss
.hide-mobile         // 手机端隐藏
.hide-tablet         // 平板端隐藏
.show-mobile         // 仅手机端显示
.show-desktop        // 仅桌面端显示
```

### 动画工具类
```scss
.transition-all      // 标准过渡
.hover-lift          // 悬停上升
.hover-scale         // 悬停缩放
```

## 最佳实践

### 1. 使用变量
```scss
// ✅ 推荐
.button {
  color: $primary-color;
  padding: $spacing-md $spacing-lg;
}

// ❌ 不推荐
.button {
  color: #313131;
  padding: 15px 20px;
}
```

### 2. 使用混入
```scss
// ✅ 推荐
.card {
  @include flex-center;
  @include shadow-light;

  @include tablet-only {
    @include flex-column;
  }
}

// ❌ 不推荐
.card {
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

@media (max-width: 767px) {
  .card {
    flex-direction: column;
  }
}
```

### 3. 嵌套语法
```scss
.component {
  color: $text-primary;

  &__title {
    @include font-primary($font-size-xl, $font-weight-bold);
  }

  &__content {
    @include font-secondary;

    &:hover {
      color: $primary-color;
    }
  }

  &--active {
    background: $bg-primary;
  }
}
```

### 4. 组件样式组织
```scss
// 组件根元素
.my-component {
  // 基础样式

  // 子元素
  &__header { }
  &__content { }
  &__footer { }

  // 修饰符
  &--large { }
  &--small { }

  // 状态
  &:hover { }
  &:active { }
  &.is-active { }

  // 响应式
  @include tablet-only { }
  @include mobile-only { }
}
```
