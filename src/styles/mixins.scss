// ===== 响应式断点混入 =====
@mixin mobile-only {// ≤479px
  @media (max-width: #{$breakpoint-xs - 1px}) {
    @content;
  }
}

@mixin mobile-up {// ≥480px
  @media (min-width: $breakpoint-xs) {
    @content;
  }
}

@mixin tablet-only {// ≤767px
  @media (max-width: #{$breakpoint-sm - 1px}) {
    @content;
  }
}

@mixin tablet-up {// ≥768px
  @media (min-width: $breakpoint-sm) {
    @content;
  }
}

@mixin medium-only {// ≤1023px
  @media (max-width: #{$breakpoint-md - 1px}) {
    @content;
  }
}

@mixin medium-up {// ≥1024px
  @media (min-width: $breakpoint-md) {
    @content;
  }
}

@mixin desktop-only {// ≤1279px
  @media (max-width: #{$breakpoint-lg - 1px}) {
    @content;
  }
}

@mixin desktop-up {// ≥1280px
  @media (min-width: $breakpoint-lg) {
    @content;
  }
}

@mixin large-up {
  @media (min-width: $breakpoint-xl) {
    @content;
  }
}

// ===== 容器混入 =====
@mixin container {
  max-width: $container-max-width;
  margin: 148px auto 180px;
  padding: 0 0;

  @include desktop-only {
    margin: 100px auto 120px;
    padding: 0 $container-padding-tablet;
  }

  @include medium-only{
    margin: 100px auto 120px;
    padding: 0 $container-padding-40;
  }

  @include tablet-only {
    margin: 100px auto 120px;
    padding: 0 $container-padding-mobile;
  }

  @include mobile-only {
    margin: 50px auto 100px;
    padding: 0 $container-padding-small;
  }
}

@mixin container-narrow {
  max-width: $container-narrow-width;
  margin: 0 auto;
  padding: 0 $container-padding-desktop;

  @include desktop-only {
    padding: 0 $container-padding-tablet;
  }

  @include tablet-only {
    padding: 0 $container-padding-mobile;
  }

  @include mobile-only {
    padding: 0 $container-padding-small;
  }
}

@mixin container-wide {
  max-width: $container-wide-width;
  margin: 0 auto;
  padding: 0 $container-padding-desktop;

  @include desktop-only {
    padding: 0 $container-padding-tablet;
  }

  @include tablet-only {
    padding: 0 $container-padding-mobile;
  }

  @include mobile-only {
    padding: 0 $container-padding-small;
  }
}

// ===== 字体混入 =====
// 注意：详细的字体混入在 fonts.scss 中定义

// 中文字体混入
@mixin font-chinese($size: $font-size-base, $weight: $font-weight-normal, $line-height: 1.5) {
  font-family: $font-family-chinese;
  font-size: $size;
  font-weight: $weight;
  line-height: $line-height;
  font-feature-settings: 'kern' 1;
}

// 英文字体混入
@mixin font-english($size: $font-size-base, $weight: $font-weight-normal, $line-height: 1.4) {
  font-family: $font-family-english;
  font-size: $size;
  font-weight: $weight;
  line-height: $line-height;
  font-feature-settings: 'kern' 1;
}

// 主要字体混入（中文优先）
@mixin font-primary($size: $font-size-base, $weight: $font-weight-normal, $line-height: 1.5) {
  font-family: $font-family-primary;
  font-size: $size;
  font-weight: $weight;
  line-height: $line-height;
  font-feature-settings: 'kern' 1;
}

// 次要字体混入（英文优先）
@mixin font-secondary($size: $font-size-base, $weight: $font-weight-normal, $line-height: 1.4) {
  font-family: $font-family-secondary;
  font-size: $size;
  font-weight: $weight;
  line-height: $line-height;
  font-feature-settings: 'kern' 1;
}

// 默认字体混入
@mixin font-default($size: $font-size-base, $weight: $font-weight-normal, $line-height: 1.5) {
  font-family: $font-family-default;
  font-size: $size;
  font-weight: $weight;
  line-height: $line-height;
  font-feature-settings: 'kern' 1;
}

// ===== 文字样式混入 =====
@mixin text-center {
  text-align: center;
}

@mixin text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin text-clamp($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// ===== 布局混入 =====
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@mixin flex-baseline {
  display: flex;
  align-items: baseline;
  justify-content: space-between;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

@mixin flex-column-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// ===== 网格混入 =====
@mixin grid-responsive($min-width: 300px, $gap: $grid-gap-medium) {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax($min-width, 1fr));
  gap: $gap;
}

// ===== 过渡混入 =====
@mixin transition($property: all, $duration: $transition-normal, $timing: ease) {
  transition: $property $duration $timing;
}

@mixin transition-bounce {
  transition: transform $transition-slower $ease-out-back;
}

// ===== 阴影混入 =====
@mixin shadow-light {
  box-shadow: 0 4px 20px $shadow-light;
}

@mixin shadow-medium {
  box-shadow: 0 8px 30px $shadow-medium;
}

@mixin shadow-dark {
  box-shadow: 0 12px 40px $shadow-dark;
}

// ===== 滚动条混入 =====
@mixin scrollbar-thin($thumb-color: rgba($primary-color, 0.3)) {
  scrollbar-width: thin;
  scrollbar-color: $thumb-color transparent;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: $thumb-color;
    border-radius: 3px;

    &:hover {
      background: rgba($primary-color, 0.5);
    }
  }
}

// ===== 动画混入 =====
@mixin hover-lift($distance: 5px) {
  transition: transform $transition-normal ease;

  &:hover {
    transform: translateY(-$distance);
  }
}

@mixin hover-scale($scale: 1.05) {
  transition: transform $transition-normal ease;

  &:hover {
    transform: scale($scale);
  }
}

// ===== 图片混入 =====
@mixin image-cover {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

@mixin image-contain {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

// ===== 按钮混入 =====
@mixin button-reset {
  border: none;
  background: transparent;
  padding: 0;
  margin: 0;
  cursor: pointer;
  outline: none;
}

// ===== 清除浮动混入 =====
@mixin clearfix {
  &::after {
    content: "";
    display: table;
    clear: both;
  }
}
