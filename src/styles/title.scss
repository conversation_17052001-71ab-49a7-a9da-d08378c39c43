@import "variables";
@import "fonts";
@import "mixins";

// 标题部分
.title-section {
  @include flex-between;
  align-items: flex-end;
  gap: 10px;
  padding-bottom: 60px;
  border-bottom: 1px solid #868686;
  width: 1320px;
  max-width: 100%;
  margin: 0 auto;

  @include tablet-only {
    width: 100%;
    padding: 0 ;
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }
}

.mt180 {
  margin-top: 180px;
  @include desktop-only{
    margin-top: 100px;
  }
  @include tablet-only {
    margin-top: 50px;
  }
  @include mobile-only {
    margin-top: 30px;
  }
}

.title-with-pagination {
  @include flex-between;
  align-items: flex-end;
  width: 100%;

  @include tablet-only {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}

.title {
  font-family: $font-family-chinese;
  font-weight: 300;
  font-size: 100px;
  line-height: 1em;
  color: $text-primary;
  margin: 0;

  @include desktop-only{
    font-size: 70px;
  }
  @include tablet-only {
    font-size: 50px;
  }
}

.pagination-info {
  font-family: $font-family-chinese;
  font-weight: 400;
  font-size: 20px;
  line-height: 1em;
  color: $text-secondary;

  @include tablet-only {
    font-size: 16px;
  }
}
// 返回按钮部分
.back-section {
  @include flex-center;
  gap: 5px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateX(-2px);
  }

  @include tablet-only {
    align-self: flex-end;
  }
}

.back-button {
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  @include flex-center;
  img {
    width: 28px;
    height: 28px;
  }
}

.page-section {
  @include flex-center;
  gap: 10px;
}

.pre-button,
.next-button {
  transition: all 0.3s ease;

  &.disabled {
    pointer-events: none;

    .pre-icon,
    .next-icon {
      cursor: not-allowed;
    }
  }

  &:hover:not(.disabled) {
    transform: scale(1.1);
  }
}

.pre-icon {
  width: 28px;
  height: 28px;
  position: relative;
  cursor: pointer;

  // 水平线
  &::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 6px;
    width: 15px;
    height: 1.5px;
    background-color: #000;
    transform: translateY(-50%);
  }

  // 箭头
  &::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 6.43px;
    width: 10px;
    height: 10px;
    border: 1.5px solid #000;
    border-right: none;
    border-bottom: none;
    transform: translateY(-50%) rotate(-45deg);
  }
}
.next-icon {
  width: 28px;
  height: 28px;
  position: relative;
  cursor: pointer;

  // 水平线
  &::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 6px;
    width: 15px;
    height: 1.5px;
    background-color: #000;
    transform: translateY(-50%);
  }

  // 箭头
  &::after {
    content: "";
    position: absolute;
    top: 9%;
    right: 7px;
    width: 10px;
    height: 10px;
    border: 1.5px solid #000;
    border-left: none;
    border-bottom: none;
    transform: translateY(50%) rotate(45deg);
  }
}

.back-text {
  font-family: $font-family-chinese;
  font-weight: 400;
  font-size: 20px;
  line-height: 1em;
  color: #000;

  @include tablet-only {
    font-size: 16px;
  }
}

@media (max-width: 968px) {
  .title {
    font-size: 50px;
  }

  .title-section {
    padding-bottom: 30px;
  }
}
