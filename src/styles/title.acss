@font-face {
  font-family: 'Alibaba PuHuiTi 2.0';
  src: url("/fonts/AlibabaPuHuiTi-2-35-Thin.woff2") format("woff2");
  font-weight: 200;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Alibaba PuHuiTi 2.0';
  src: url("/fonts/AlibabaPuHuiTi-2-45-Light.woff2") format("woff2");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Alibaba PuHuiTi 2.0';
  src: url("/fonts/AlibabaPuHuiTi-2-55-Regular.woff2") format("woff2");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Alibaba PuHuiTi 2.0';
  src: url("/fonts/AlibabaPuHuiTi-2-65-Medium.woff2") format("woff2");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Alibaba PuHuiTi 2.0';
  src: url("/fonts/AlibabaPuHuiTi-2-65-Medium.woff2") format("woff2");
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Alibaba PuHuiTi 2.0';
  src: url("/fonts/AlibabaPuHuiTi-2-85-Bold.woff2") format("woff2");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Inter';
  src: url("/fonts/Inter-VariableFont.woff2") format("woff2-variations");
  font-weight: 100 900;
  font-style: normal;
  font-display: swap;
  font-named-instance: 'Regular';
}

.title-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  align-items: flex-end;
  gap: 10px;
  padding-bottom: 60px;
  border-bottom: 1px solid #868686;
  width: 1320px;
  max-width: 100%;
  margin: 0 auto;
}

@media (max-width: 767px) {
  .title-section {
    width: 100%;
    padding: 0;
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }
}

.mt180 {
  margin-top: 180px;
}

@media (max-width: 1279px) {
  .mt180 {
    margin-top: 100px;
  }
}

@media (max-width: 767px) {
  .mt180 {
    margin-top: 50px;
  }
}

@media (max-width: 479px) {
  .mt180 {
    margin-top: 30px;
  }
}

.title-with-pagination {
  display: flex;
  align-items: center;
  justify-content: space-between;
  align-items: flex-end;
  width: 100%;
}

@media (max-width: 767px) {
  .title-with-pagination {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}

.title {
  font-family: "Alibaba PuHuiTi 2.0", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
  font-weight: 300;
  font-size: 100px;
  line-height: 1em;
  color: #313131;
  margin: 0;
}

@media (max-width: 1279px) {
  .title {
    font-size: 70px;
  }
}

@media (max-width: 767px) {
  .title {
    font-size: 50px;
  }
}

.pagination-info {
  font-family: "Alibaba PuHuiTi 2.0", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
  font-weight: 400;
  font-size: 20px;
  line-height: 1em;
  color: #636363;
}

@media (max-width: 767px) {
  .pagination-info {
    font-size: 16px;
  }
}

.back-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-section:hover {
  transform: translateX(-2px);
}

@media (max-width: 767px) {
  .back-section {
    align-self: flex-end;
  }
}

.back-button {
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-button img {
  width: 28px;
  height: 28px;
}

.page-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.pre-button,
.next-button {
  transition: all 0.3s ease;
}

.pre-button.disabled,
.next-button.disabled {
  pointer-events: none;
}

.pre-button.disabled .pre-icon,
.pre-button.disabled .next-icon,
.next-button.disabled .pre-icon,
.next-button.disabled .next-icon {
  cursor: not-allowed;
}

.pre-button:hover:not(.disabled),
.next-button:hover:not(.disabled) {
  transform: scale(1.1);
}

.pre-icon {
  width: 28px;
  height: 28px;
  position: relative;
  cursor: pointer;
}

.pre-icon::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 6px;
  width: 15px;
  height: 1.5px;
  background-color: #000;
  transform: translateY(-50%);
}

.pre-icon::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 6.43px;
  width: 10px;
  height: 10px;
  border: 1.5px solid #000;
  border-right: none;
  border-bottom: none;
  transform: translateY(-50%) rotate(-45deg);
}

.next-icon {
  width: 28px;
  height: 28px;
  position: relative;
  cursor: pointer;
}

.next-icon::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 6px;
  width: 15px;
  height: 1.5px;
  background-color: #000;
  transform: translateY(-50%);
}

.next-icon::after {
  content: "";
  position: absolute;
  top: 9%;
  right: 7px;
  width: 10px;
  height: 10px;
  border: 1.5px solid #000;
  border-left: none;
  border-bottom: none;
  transform: translateY(50%) rotate(45deg);
}

.back-text {
  font-family: "Alibaba PuHuiTi 2.0", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
  font-weight: 400;
  font-size: 20px;
  line-height: 1em;
  color: #000;
}

@media (max-width: 767px) {
  .back-text {
    font-size: 16px;
  }
}

@media (max-width: 968px) {
  .title {
    font-size: 50px;
  }
  .title-section {
    padding-bottom: 30px;
  }
}
