import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/Home.vue')
  },
  {
    path: '/about',
    name: 'About',
    component: () => import('../views/About.vue')
  },
  {
    path: '/services',
    name: 'Services',
    component: () => import('../views/Services.vue')
  },
  {
    path: '/service-demo',
    name: 'ServiceDemo',
    component: () => import('../views/ServiceDemo.vue')
  },
  {
    path: '/ep/:id',
    name: 'Ep',
    component: () => import('../views/Ep.vue')
  },
  {
    path: '/single/:id',
    name: 'Single',
    component: () => import('../views/Single.vue')
  },
  {
    path: '/artists/:type',
    name: 'Artists',
    component: () => import('../views/Artists.vue')
  },
  {
    path: '/detail/:type/:id',
    name: 'ArtistDetail',
    component: () => import('../views/ArtistDetail.vue')
  },
  {
    path: '/albums',
    name: 'Albums',
    component: () => import('../views/Albums.vue')
  },
  {
    path: '/albums/:id',
    name: 'AlbumDetail',
    component: () => import('../views/AlbumDetail.vue')
  },
  {
    path: '/news',
    name: 'NewsList',
    component: () => import('../views/NewsList.vue')
  },
  {
    path: '/news/:id',
    name: 'News',
    component: () => import('../views/News.vue')
  },
  {
    path: '/language-test',
    name: 'LanguageTest',
    component: () => import('../views/LanguageTest.vue')
  },
  {
    path: '/font-test',
    name: 'FontTest',
    component: () => import('../components/FontExample.vue')
  },
  {
    path: '/api-test',
    name: 'ApiTest',
    component: () => import('../views/ApiTest.vue')
  },
  {
    path: '/simple-test',
    name: 'SimpleTest',
    component: () => import('../views/SimpleTest.vue')
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('../views/NotFound.vue')
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
