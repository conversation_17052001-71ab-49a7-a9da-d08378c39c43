# Vue I18n 数组翻译修复说明

## 问题描述

在使用Vue I18n处理数组类型的翻译数据时，遇到了以下错误：
```
[intlify] Not found 'services.serviceCards.globalDistribution.features' key in 'zh' locale messages.
```

## 问题原因

Vue I18n的`$t()`函数主要用于获取字符串类型的翻译，当尝试用它来获取数组类型的翻译数据时会失败。

## 解决方案

使用`$tm()`函数代替`$t()`函数来获取数组类型的翻译数据。

### 错误的用法 ❌
```vue
<div
  v-for="(feature, index) in $t('services.serviceCards.globalDistribution.features')"
  :key="index"
  class="feature-item"
>
  {{ feature }}
</div>
```

### 正确的用法 ✅
```vue
<div
  v-for="(feature, index) in $tm('services.serviceCards.globalDistribution.features')"
  :key="index"
  class="feature-item"
>
  {{ feature }}
</div>
```

## Vue I18n API 说明

### `$t()` 函数
- 用途：获取字符串类型的翻译
- 返回：字符串
- 示例：`$t('common.loading')` → "加载中..."

### `$tm()` 函数
- 用途：获取原始翻译消息（包括数组、对象等）
- 返回：原始数据类型（数组、对象、字符串等）
- 示例：`$tm('services.serviceCards.globalDistribution.features')` → ["一键触达全球", "全流程专业护航", "最大化作品可见度"]

### `$te()` 函数
- 用途：检查翻译键是否存在
- 返回：布尔值
- 示例：`$te('services.title')` → true

## 翻译数据结构

```javascript
// zh.js
export default {
  services: {
    serviceCards: {
      globalDistribution: {
        title: '全球发行与运营',        // 字符串 - 使用 $t()
        number: '01',                  // 字符串 - 使用 $t()
        features: [                    // 数组 - 使用 $tm()
          '一键触达全球',
          '全流程专业护航',
          '最大化作品可见度'
        ],
        descriptions: [                // 数组 - 使用 $tm()
          '覆盖30+国内外主流音乐及社交平台',
          '提供从版权授权、物料制作、平台上线到内容维护的全流程发行支持',
          '确保您的音乐作品快速上线、全球可见'
        ]
      }
    }
  }
}
```

## 最佳实践

1. **字符串翻译**：使用`$t()`
   ```vue
   <h1>{{ $t('services.title') }}</h1>
   ```

2. **数组翻译**：使用`$tm()`
   ```vue
   <li v-for="item in $tm('menu.items')" :key="item">
     {{ item }}
   </li>
   ```

3. **对象翻译**：使用`$tm()`
   ```vue
   <div v-for="(value, key) in $tm('user.profile')" :key="key">
     {{ key }}: {{ value }}
   </div>
   ```

4. **条件渲染**：使用`$te()`检查键是否存在
   ```vue
   <div v-if="$te('optional.content')">
     {{ $t('optional.content') }}
   </div>
   ```

## 注意事项

1. `$tm()`返回的是原始数据，如果是字符串数组，可以直接在模板中使用
2. 如果数组中包含需要进一步翻译的键，需要在循环中再次使用`$t()`
3. 确保在Vue I18n配置中启用了`globalInjection: true`以在模板中使用这些函数

## 相关文件

- `src/components/ServiceCards.vue` - 主要修复文件
- `src/components/__tests__/ServiceCards.test.js` - 测试文件更新
- `src/i18n/locales/*.js` - 翻译文件

## 参考资料

- [Vue I18n 官方文档](https://vue-i18n.intlify.dev/)
- [Vue I18n API 参考](https://vue-i18n.intlify.dev/api/general.html)
