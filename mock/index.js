import Mock from 'mockjs'

// 生成完整的新闻数据数组
const total = 23
const allNews = Mock.mock({
  [`list|${total}`]: [
    {
      'id|+1': 1,
      title: '@ctitle(10, 18)',
      date: '@date("yyyy-MM-dd")',
      author: '@cname',
      tag: '@pick(["新闻", "活动", "公告"])',
      cover: 'https://picsum.photos/seed/@id/600/300',
      content: '@cparagraph(3, 8)'
    }
  ]
}).list

// 新闻列表 mock，支持分页
console.log("执行到拦截",Mock.mock('/api/news'))
Mock.mock(RegExp('/api/news$'), 'get', (options) => {
  const url = new URL('http://dummy.com' + options.url)

  const page = Number(url.searchParams.get('page')) || 1
  const pageSize = Number(url.searchParams.get('pageSize')) || 5
  const start = (page - 1) * pageSize
  const end = start + pageSize
  const list = allNews.slice(start, end)
  return {
    code: 0,
    data: {
      list,
      total
    }
  }
})

// 新闻详情 mock
Mock.mock(RegExp('/api/news/\\d+'), 'get', (options) => {
  const url = options.url
  const id = parseInt(url.split('/').pop())

  // 查找对应的新闻
  const news = allNews.find(item => item.id === id)

  if (!news) {
    return {
      code: 404,
      message: '新闻不存在'
    }
  }

  // 生成详细内容
  const detailContent = Mock.mock({
    content: '@cparagraph(8, 15)',
    publishTime: '@datetime("yyyy-MM-dd HH:mm:ss")',
    readCount: '@integer(100, 9999)',
    tags: '@pick(["新闻", "活动", "公告", "艺人动态", "专辑发布"])',
    images: [
      'https://picsum.photos/seed/@id/800/600',
      'https://picsum.photos/seed/@increment/800/600'
    ]
  })

  return {
    code: 0,
    data: {
      ...news,
      ...detailContent
    }
  }
})

// banner mock
Mock.mock('/api/banner', 'get', {
  code: 0,
  data: [
    { id: 1, img: 'https://picsum.photos/seed/banner1/1200/400', link: '#' },
    { id: 2, img: 'https://picsum.photos/seed/banner2/1200/400', link: '#' }
  ]
}) 

//艺人，词曲人，制作人
Mock.mock(RegExp('/api/personList' + ".*"), 'get',{
    code: 0,
    data:[{
    id: "weihan",
    name: "魏晗",
    alias: "KIIRAS",
    category: "合作艺人",
  
  },
  {
    id: "kiiras",
    name: "KIIRAS",
    alias: "魏晗",
    category: "合作艺人",
 
  },
  {
    id: "kii",
    name: "haha",
    alias: "魏晗1",
    category: "Alibaba PuHuiTi 2.0",

  },
  {
    id: "weihan1",
    name: "魏晗",
    alias: "KIIRAS",
    category: "合作艺人",
  
  },
  {
    id: "kiiras2",
    name: "KIIRAS",
    alias: "魏晗",
    category: "合作艺人",
   
  },
  {
    id: "kii3",
    name: "haha",
    alias: "魏晗1",
    category: "合作艺人",

  },
  {
    id: "weihan4",
    name: "魏晗",
    alias: "KIIRAS",
    category: "自由",
   
  },
  {
    id: "kiiras5",
    name: "KIIRAS",
    alias: "魏晗",
    category: "[合作艺人]",
  
  },
  {
    id: "kii6",
    name: "haha",
    alias: "魏晗1",
    category: "[合作艺人]",
   
  },]
})

// 专辑EP数据 mock
